<template>
  <div class="bazi-result-display">
    <!-- 档案信息 -->
    <div class="profile-info">
      <div class="info-line">
        <span class="info-label">真太阳时</span>
        <span class="info-icon">⊙</span>
        <span class="info-text">{{ profile?.realSolarTime || '-' }}</span>
        <span class="info-icon">👁</span>
      </div>
      <div class="info-line">
        <span class="info-label">出生节气</span>
        <span class="info-text">{{ result?.birthSolarTerms || '-' }}</span>
      </div>
    </div>

    <!-- 八字表格 -->
    <div class="bazi-card" v-if="result">
      <div class="bazi-table-container">
        <div class="bazi-table">
          <!-- 表头 -->
          <div class="table-header">
            <div class="header-cell category-header"></div>
            <div class="header-cell">年柱</div>
            <div class="header-cell">月柱</div>
            <div class="header-cell">日柱</div>
            <div class="header-cell">时柱</div>
          </div>

          <!-- 干神行 -->
          <div class="table-row">
            <div class="row-cell category-cell">干神</div>
            <div class="row-cell" v-for="index in 4" :key="`ganshen-${index - 1}`">
              {{ getZhuXing(index - 1) || '-' }}
            </div>
          </div>

          <!-- 天干行 -->
          <div class="table-row">
            <div class="row-cell category-cell">天干</div>
            <div class="row-cell tiangan-cell" v-for="index in 4" :key="`tiangan-${index - 1}`"
              :style="{ color: getTianGanColor(index - 1) }">
              {{ getTianGan(index - 1) || "-" }}
            </div>
          </div>

          <!-- 地支行 -->
          <div class="table-row">
            <div class="row-cell category-cell">地支</div>
            <div class="row-cell dizhi-cell" v-for="index in 4" :key="`dizhi-${index - 1}`"
              :style="{ color: getDiZhiColor(index - 1) }">
              {{ getDiZhi(index - 1) || "-" }}
            </div>
          </div>

          <!-- 藏干行 -->
          <div class="table-row">
            <div class="row-cell category-cell">藏干</div>
            <div class="row-cell canggan-cell" v-for="index in 4" :key="`canggan-${index - 1}`">
              <div class="canggan-content">
                <div v-if="getCangGan(index - 1).length === 0" class="empty-placeholder">-</div>
                <div v-else v-for="(canggan, cgIndex) in getCangGan(index - 1)" :key="cgIndex" class="canggan-item"
                  :style="{ color: getCangGanItemColor(canggan) }">
                  {{ canggan }}
                </div>
              </div>
            </div>
          </div>

          <!-- 支神行 -->
          <div class="table-row">
            <div class="row-cell category-cell">支神</div>
            <div class="row-cell" v-for="index in 4" :key="`zhishen-${index - 1}`">
              <div class="zhishen-content">
                <div v-if="getFuXing(index - 1).length === 0" class="empty-placeholder">-</div>
                <div v-else v-for="(fuxing, fxIndex) in getFuXing(index - 1)" :key="fxIndex" class="fuxing-item">
                  {{ fuxing }}
                </div>
              </div>
            </div>
          </div>

          <!-- 纳音行 -->
          <div class="table-row">
            <div class="row-cell category-cell">纳音</div>
            <div class="row-cell nayin-cell" v-for="index in 4" :key="`nayin-${index - 1}`">
              {{ getNaYin(index - 1) || "-" }}
            </div>
          </div>

          <!-- 空亡行 -->
          <div class="table-row">
            <div class="row-cell category-cell">空亡</div>
            <div class="row-cell" v-for="index in 4" :key="`kongwang-${index - 1}`">
              {{ getKongWang(index - 1) || "-" }}
            </div>
          </div>

          <!-- 地势行 -->
          <div class="table-row">
            <div class="row-cell category-cell">地势</div>
            <div class="row-cell" v-for="index in 4" :key="`dishi-${index - 1}`">
              {{ getDiShi(index - 1) || "-" }}
            </div>
          </div>

          <!-- 神煞行 -->
          <div class="table-row">
            <div class="row-cell category-cell">神煞</div>
            <div class="row-cell" v-for="index in 4" :key="`shensha-${index - 1}`">
              <div class="shensha-content">
                <div v-if="getShenSha(index - 1).length === 0" class="empty-placeholder">-</div>
                <div v-else v-for="(shensha, ssIndex) in getShenSha(index - 1)" :key="ssIndex" class="shensha-item">
                  {{ shensha }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 生辰解读 -->
    <BirthInterpretation :result="result" :profile="profile" />

  </div>
</template>

<script setup lang="ts">
import type { Profile } from "@/views/profile/profile";
import BirthInterpretation from "./BirthInterpretation.vue";

const props = defineProps<{
  result: any;
  profile: Profile;
}>();

// 监听真实数据变化并输出
watchEffect(() => {
  if (props.result && props.profile) {
    console.log("=== 真实八字排盘结果数据 ===");
    console.log("档案信息:", props.profile);
    console.log("八字排盘结果:", props.result);
    console.log("=== 数据输出完毕 ===");
  }
});

// 格式化出生日期
const formatBirthDate = () => {
  if (!props.profile) return "";

  if (props.profile.profileDateType === 1) {
    return `${props.profile.solarBirth} (公历)`;
  } else if (props.profile.profileDateType === 2) {
    return `${props.profile.lunarBirth} (农历)`;
  } else {
    return props.profile.fourPillars;
  }
};

// 格式化地点
const formatLocation = () => {
  if (!props.profile) return "";
  return `经度: ${props.profile.longitude}, 纬度: ${props.profile.latitude}`;
};

import {
  getTianGan as getTianGanUtil,
  getDiZhi as getDiZhiUtil,
  getZhuXing as getZhuXingUtil,
  getCangGan as getCangGanUtil,
  getFuXing as getFuXingUtil,
  getNaYin as getNaYinUtil,
  getKongWang as getKongWangUtil,
  getDiShi as getDiShiUtil,
  getShenSha as getShenShaUtil
} from '@/utils/baziDataUtils';

import {
  getTianGanColor as getTianGanColorUtil,
  getDiZhiColor as getDiZhiColorUtil,
  getCangGanColor
} from '@/utils/wuxingColors';

// 使用工具函数的包装器
const getTianGan = (index: number) => getTianGanUtil(props.result, index);
const getDiZhi = (index: number) => getDiZhiUtil(props.result, index);
const getZhuXing = (index: number) => getZhuXingUtil(props.result, index);
const getCangGan = (index: number) => getCangGanUtil(props.result, index);
const getFuXing = (index: number) => getFuXingUtil(props.result, index);
const getNaYin = (index: number) => getNaYinUtil(props.result, index);
const getKongWang = (index: number) => getKongWangUtil(props.result, index);
const getDiShi = (index: number) => getDiShiUtil(props.result, index);
const getShenSha = (index: number) => getShenShaUtil(props.result, index);

// 五行颜色获取函数
const getTianGanColor = (index: number) => {
  const tianganChar = getTianGan(index);
  return getTianGanColorUtil(tianganChar);
};

const getDiZhiColor = (index: number) => {
  const dizhiChar = getDiZhi(index);
  return getDiZhiColorUtil(dizhiChar);
};

const getCangGanItemColor = (cangganChar: string) => {
  return getCangGanColor(cangganChar);
};


</script>

<style scoped lang="less">
.bazi-result-display {
  padding: 17px 16px 0;
}

/* 档案信息样式 */
.profile-info {
  margin-bottom: 16px;
}

.info-line {
  display: flex;
  align-items: center;
  padding: 8px 0;
  font-size: 14px;
  color: #333;

  .info-label {
    font-weight: 500;
    margin-right: 8px;
  }

  .info-icon {
    margin: 0 4px;
    font-size: 12px;
  }

  .info-text {
    flex: 1;
  }
}

/* 卡片通用样式 */
.bazi-card {
  background: linear-gradient(0deg, rgba(0, 0, 0, 0.001), rgba(0, 0, 0, 0.001)), #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  margin-bottom: 16px;
  box-shadow: 0px 10px 15px -3px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(6px);
}

/* 八字表格样式 */
.bazi-table-container {
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

.bazi-table {
  width: 100%;
  min-width: 480px;
  border-collapse: collapse;
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
}

/* 表头样式 */
.table-header {
  display: flex;
  background-color: #F2EFE9;
  color: #333;
  font-weight: 600;
}

.header-cell {
  flex: 1;
  padding: 12px 8px;
  text-align: center;
  font-size: 14px;
}

.category-header {
  flex: 0 0 60px;
}

/* 表格行样式 */
.table-row {
  display: flex;

  // 深色背景行：干神、藏干、纳音、地势
  &:nth-child(2),
  // 干神
  &:nth-child(5),
  // 藏干
  &:nth-child(7),
  // 纳音
  &:nth-child(9) {
    // 地势
    background-color: #F2EFE9;
  }

  // 浅色背景行：天干、地支、支神、空亡、神煞
  &:nth-child(3),
  // 天干
  &:nth-child(4),
  // 地支
  &:nth-child(6),
  // 支神
  &:nth-child(8),
  // 空亡
  &:nth-child(10) {
    // 神煞
    background-color: #F6F5F1;
  }
}

.row-cell {
  flex: 1;
  padding: 12px 8px;
  text-align: center;
  font-size: 13px;
  min-height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.category-cell {
  flex: 0 0 60px;
  font-weight: 600;
  color: #8b7355;
  font-size: 12px;
}

/* 特殊单元格样式 */
.tiangan-cell {
  font-size: 24px;
  font-weight: bold;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
}

.dizhi-cell {
  font-size: 24px;
  font-weight: bold;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
}

.nayin-cell {
  font-weight: 500;
  font-size: 12px;
}

/* 多行内容样式 */
.canggan-content,
.zhishen-content,
.shensha-content {
  display: flex;
  flex-direction: column;
  gap: 1px;
  width: 100%;
  align-items: center;
}

.canggan-item,
.fuxing-item,
.shensha-item {
  font-size: 11px;
  line-height: 1.2;
  padding: 1px 0;
  margin: 1px 0;
  white-space: nowrap;
}

/* 空占位符样式 */
.empty-placeholder {
  color: #9ca3af;
  font-size: 12px;
  font-style: italic;
}
</style>
