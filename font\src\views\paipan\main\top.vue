<template>
  <van-nav-bar :title="$t('paipan.main.top')">
    <template #left>
      <van-icon
        name="arrow-left"
        size="20px"
        class="nav-back-icon"
        @click="goback"
      />
    </template>
    <template #right>
      <span>logo</span>
    </template>
  </van-nav-bar>
</template>

<script setup lang="ts">
const router = useRouter();
const goback = () => {
  router.back();
};
</script>

<style lang="less" scoped>
.nav-back-icon {
  color: #ffffff;
}

:deep(.van-nav-bar__content) {
  height: 60px;
  background:
    linear-gradient(0deg, rgba(0, 0, 0, 0), rgba(0, 0, 0, 0)),
    linear-gradient(144deg, #6366f1 -15%, #818cf8 115%);
}

:deep(.van-nav-bar__title) {
  font-family: Roboto;
  font-size: 18px;
  font-weight: 500;
  line-height: 28px;
  letter-spacing: 0px;

  font-feature-settings: "kern" on;
  color: #ffffff;
}
</style>
