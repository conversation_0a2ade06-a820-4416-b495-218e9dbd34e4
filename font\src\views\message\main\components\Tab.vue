<template>
  <div class="flex">
    <button
      class="flex-1 font"
      :class="{ 'active-font': active === 2 }"
      @click="setActive(2)"
    >
      已发布
    </button>
    <button
      class="flex-1 font"
      :class="{ 'active-font': active === 3 }"
      @click="setActive(3)"
    >
      已回复
    </button>
  </div>
</template>

<script setup lang="ts">
const active = ref(2); // 2 已发布 3 已回复
const emit = defineEmits(["updateActive"]);

const setActive = (value: number) => {
  active.value = value;
  emit("updateActive", value);
};
</script>

<style scoped>
button {
  height: 44px;
  border-radius: 10px;
  background: rgba(0, 0, 0, 0.01);
  box-shadow: 0 4px 10px 0 rgba(0, 0, 0, 0.1);
}

.font {
  font-family: Roboto;
  font-size: 14px;
  font-weight: 500;
  line-height: 21px;
  text-align: center;
  letter-spacing: 0px;
  font-feature-settings: "kern" on;
  color: #4b5563;
}

.active-font {
  color: #92abff;
}
</style>
