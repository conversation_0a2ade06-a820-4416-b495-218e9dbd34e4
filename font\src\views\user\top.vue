<template>
  <div class="nav-top">
    <span style="color: white">{{ $t("user.top") }}</span>
    <svg-icon
      name="user-setting"
      class-name="setting-icon"
      @click="toSetting"
    />
  </div>
</template>

<script setup lang="ts">
const router = useRouter();
const toSetting = () => {
  router.push("/setting");
};
</script>
<style scoped>
.nav-top {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  row-gap: 0px;
  flex-wrap: wrap;
  align-content: center;

  background: #92abff;

  box-sizing: border-box;
  border-width: 0px 0px 1px 0px;
  border-style: solid;
  border-color: #e5e7eb;

  z-index: 1;
}

.setting-icon {
  width: 32px;
  height: 32px;
}
</style>
