export interface Message {
  messageId: string;
  messageName: string;
  messageContent: string;
  sendTime: Date;
}

export interface Discussion {
  discussionId: string;
  postTitle: string;
  postContent: string;
  postImgUrls: string;
  status: number;
  updateTime: Date;
  publishTime: Date;
  reply: string;
  replyTime: Date;
  replyUser: string;
}

export interface Activity {
  activityId: string;
  activityName: string;
  description: string;
  cover: string;
  activityType: number;
  publishTime: Date;
}
