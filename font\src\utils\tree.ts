// 判断数组是否可用
const isAvailableArray = (arr: string | any[]) =>
  Array.isArray(arr) && arr.length > 0;

// 扁平化数组方法
export function flatTree(array: any[], parentId: any) {
  return array.reduce((tree, cur) => {
    // 拿到遍历数据的 children
    const { children } = cur;
    // return 返回新的数组
    return [
      ...tree, // 解构 tree
      parentId ? { ...cur, parentId } : cur,
      // 判断当前的children是否可用，可用时直接继续调用flatTree方法，否则就是空数组
      ...(isAvailableArray(children) ? flatTree(children, cur.id) : [])
    ];
  }, []);
}

// 扁平化数组转树型结构
// dataFormat.value = convertTree(data.value, '', 'childrenList')
export function convertTree(
  flatArrayData: any[],
  parentId: string,
  tagChildren = "children"
) {
  const list: any[] = [];
  flatArrayData.forEach(item => {
    if (item.parentId === item.id) {
      item.parentId = "";
    }
    item[tagChildren] = flatArrayData.filter(i => i.parentId === item.id);

    if (item.parentId === parentId) {
      list.push(item);
    }
  });
  return list;
}
