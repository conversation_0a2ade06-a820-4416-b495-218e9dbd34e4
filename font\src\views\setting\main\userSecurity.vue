<template>
  <div class="box">
    <div class="w-full card-title">{{ $t("setting.security") }}</div>
    <div class="box-item">
      <svg-icon name="change-password" />
      <p class="item-font">{{ $t("setting.password") }}</p>
      <van-icon
        name="arrow"
        class="placeholder ml-auto"
        @click="toResetPassword"
      />
    </div>
    <div class="box-item">
      <svg-icon name="bx-mail-send" />
      <p class="item-font">{{ $t("setting.email") }}</p>
      <div class="flex items-center ml-auto">
        <p v-if="userInfo.username !== ''" class="placeholder mr-2">
          {{ $t("setting.exist") }}
        </p>
        <van-icon name="arrow" class="placeholder" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useUserStore } from "@/store/modules/user";

const store = useUserStore();

const { userInfo } = storeToRefs(store);
const router = useRouter();
const toResetPassword = () => {
  router.push("/password/reset");
};

const toBindEmail = () => {
  router.push("/email/bind");
};
</script>

<style scoped>
.box {
  /* 自动布局子元素 */
  width: 343px;
  height: 200px;
  padding: 16px 16px 30px 16px;
  border-radius: 12px;
  opacity: 1;

  background:
    linear-gradient(0deg, rgba(0, 0, 0, 0.001), rgba(0, 0, 0, 0.001)), #ffffff;

  box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.05);
}

.card-title {
  font-family: Roboto;
  font-size: 16px;
  font-weight: 500;
  line-height: 24px;
  letter-spacing: 0px;
  padding-bottom: 8px;

  font-feature-settings: "kern" on;
  color: #000000;
}

.box-item {
  /* 自动布局 */
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 20px 0;
  flex-wrap: wrap;
  align-content: center;

  background: rgba(0, 0, 0, 0);

  z-index: 2;

  border-top: 1px solid #e2e8f0;
}

.item-font {
  padding-left: 12px;

  font-family: Roboto;
  font-size: 16px;
  font-weight: normal;
  line-height: 24px;
  letter-spacing: 0px;

  font-feature-settings: "kern" on;
  color: #000000;
}

.placeholder {
  color: #9ca3af;
}
</style>
