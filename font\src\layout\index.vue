<script setup lang="ts">
import tabbar from "@/components/Tabbar/index.vue";
import { useCachedViewStoreHook } from "@/store/modules/cachedView";
// import { useDarkMode } from "@/hooks/useToggleDarkMode";
import { computed } from "vue";
import { useRoute } from "vue-router";

const cachedViews = computed(() => {
  return useCachedViewStoreHook().cachedViewList;
});
const route = useRoute();
const isNoTab = computed(() => {
  return route.meta?.noTab;
});
</script>

<template>
  <div class="app-wrapper">
    <van-config-provider class="van-config-provider">
      <div class="main-content">
        <router-view v-slot="{ Component }">
          <keep-alive :include="cachedViews">
            <component :is="Component" />
          </keep-alive>
        </router-view>
      </div>
      <tabbar v-if="!isNoTab" />
    </van-config-provider>
  </div>
</template>

<style lang="less" scoped>
.app-wrapper {
  position: relative;
  height: 100vh;
  width: 100%;
  display: flex;
  flex-direction: column;

  .van-config-provider {
    display: flex;
    flex-direction: column;
    flex: 1;
    overflow: hidden;
  }

  .main-content {
    flex: 1; // 让主内容区占据导航栏和标签栏之间的所有剩余空间
    overflow-y: auto; // 当内容超出时，允许垂直滚动
  }
}
</style>
