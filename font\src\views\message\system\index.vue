<template>
  <div class="h-full flex flex-col">
    <message-top :toggle-show-delete="toggleShowDelete" />
    <message-list :show-delete="showDelete" />
  </div>
</template>
<script setup lang="ts">
import MessageTop from "./top.vue";
import MessageList from "./components/MessageList.vue";

const showDelete = ref(false);

const toggleShowDelete = () => {
  showDelete.value = !showDelete.value;
};
</script>
<style scoped></style>
