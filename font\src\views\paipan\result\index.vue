<template>
  <div class="bazi-result-page">
    <Top />

    <!-- Tab切换 -->
    <van-tabs v-model:active="activeTab" class="bazi-tabs">
      <van-tab title="生辰" name="shengchen">
        <div v-if="loading" class="loading-container">
          <van-loading type="spinner" color="#8a82f1">加载中...</van-loading>
        </div>
        <div v-else-if="error" class="error-container">
          <van-empty description="加载失败，请重试" />
          <button class="action-btn" @click="loadBaziResult">重新加载</button>
        </div>
        <div v-else-if="baziResult" class="result-container">
          <BaziResultDisplay :result="baziResult" :profile="profileData" />
        </div>
        <div v-else class="empty-container">
          <van-empty description="暂无排盘结果" />
          <button class="action-btn" @click="goToCreate">重新排盘</button>
        </div>
      </van-tab>

      <van-tab title="流盘" name="liupan">
        <div v-if="liupanLoading" class="loading-container">
          <van-loading type="spinner" color="#8a82f1">流盘初始化中...</van-loading>
        </div>
        <div v-else-if="liupanError" class="error-container">
          <van-empty description="流盘初始化失败，请重试" />
          <button class="action-btn" @click="initializeLiupan">
            重新初始化
          </button>
        </div>
        <div v-else-if="liupanResult && baziResult" class="result-container">
          <LiupanResultDisplay :baziResult="baziResult" :liupanResult="liupanResult" :profileData="profileData"
            :onDaYunUpdate="handleDaYunUpdate" :onLiuNianUpdate="handleLiuNianUpdate"
            :onLiuYueUpdate="handleLiuYueUpdate" />
        </div>
        <div v-else class="liupan-placeholder">
          <van-empty description="请先完成八字排盘" />
        </div>
      </van-tab>
    </van-tabs>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, nextTick } from "vue";
import { useRoute, useRouter } from "vue-router";
import { useData } from "@/hooks/useData";
import api from "@/api/index";
import type { Profile } from "@/views/profile/profile";
import Top from "./top.vue";
import BaziResultDisplay from "./components/BaziResultDisplay.vue";
import LiupanResultDisplay from "./components/LiupanResultDisplay.vue";

const route = useRoute();
const router = useRouter();

// Tab状态管理
const activeTab = ref("shengchen");

// 获取档案信息
const {
  data: profileData,
  execute: executeGetProfile,
  onFetchResponse: onFetchProfileResponse
} = useData(api.profile.getProfileById);

// 获取八字排盘结果
const {
  data: baziResult,
  loading,
  error,
  execute: executePaipanStarts,
  onFetchResponse: onFetchPaipanResponse
} = useData(api.paipan.paipanStarts);

// 获取流盘初始化结果
const {
  data: liupanResult,
  loading: liupanLoading,
  error: liupanError,
  execute: executeLiupanInitialize,
  onFetchResponse: onFetchLiupanResponse
} = useData(api.paipan.daYunLiuNianInitialize);

// 流盘初始化响应处理 - 修复大运第一项的空值
onFetchLiupanResponse((response: any) => {
  if (response && response.daYun && response.xiaoYun) {
    // 检查大运第一项是否有空值
    const firstDaYun = response.daYun[0];
    const firstXiaoYun = response.xiaoYun[0];

    if (
      firstDaYun &&
      firstXiaoYun &&
      Array.isArray(firstDaYun) &&
      Array.isArray(firstXiaoYun)
    ) {
      // 如果大运第一项的后三个值为空，使用小运第一项的后三个值填充
      if (!firstDaYun[2] && !firstDaYun[3] && !firstDaYun[4]) {
        firstDaYun[2] = firstXiaoYun[2] || ""; // 干支
        firstDaYun[3] = firstXiaoYun[3] || ""; // 十神1
        firstDaYun[4] = firstXiaoYun[4] || ""; // 十神2

        console.log("🔥 使用小运数据修复大运第一项", {
          原始大运第一项: [...firstDaYun],
          小运第一项: firstXiaoYun,
          修复后大运第一项: firstDaYun
        });
      }
    }
  }
});

// 加载八字排盘结果
const loadBaziResult = async () => {
  const profileId = route.query.profileId as string;

  if (!profileId) {
    router.push({ name: "createPaipan" });
    return;
  }

  // 先获取档案信息
  await executeGetProfile({ id: profileId });
};

// 档案信息获取成功后，进行八字排盘
onFetchProfileResponse((profile: Profile) => {
  if (profile) {
    // 使用档案信息进行八字排盘
    executePaipanStarts({
      ...profile,
      isSave: false // 不保存档案，只进行排盘
    });
  }
});

// 流盘初始化函数
const initializeLiupan = async () => {
  if (!baziResult.value || !profileData.value) {
    return;
  }

  // 构建流盘初始化参数
  const liupanParams = {
    yearGan: baziResult.value.baZi?.[0]?.charAt(0) || "",
    monthGan: baziResult.value.baZi?.[1]?.charAt(0) || "",
    dayGan: baziResult.value.baZi?.[2]?.charAt(0) || "",
    hourGan: baziResult.value.baZi?.[3]?.charAt(0) || "",
    yearZhi: baziResult.value.baZi?.[0]?.charAt(1) || "",
    monthZhi: baziResult.value.baZi?.[1]?.charAt(1) || "",
    dayZhi: baziResult.value.baZi?.[2]?.charAt(1) || "",
    hourZhi: baziResult.value.baZi?.[3]?.charAt(1) || "",
    date: profileData.value.realSolarTime || "",
    season: baziResult.value.season || "",
    yearGanZhiNaYinWuXing: baziResult.value.yearGanZhiNaYin?.slice(-1) || "",
    sex: profileData.value.gender || 0,
    qiYunLiuPai: 1
  };

  await executeLiupanInitialize(liupanParams);
};

// 处理大运更新
const handleDaYunUpdate = async (newLiupanData: any) => {
  console.log("🔥 父组件接收到数据", newLiupanData);
  console.log("🔥 更新前 liupanResult", liupanResult.value);

  // 保存原有的大运数组，防止被覆盖
  const originalDaYun = liupanResult.value?.daYun;

  // 更新流盘结果数据 - 直接替换整个对象来确保响应式更新
  const updatedData = {
    ...liupanResult.value,
    ...newLiupanData
  };

  // 如果新数据中没有大运数组或大运数组为空，保持原有的大运数组
  if (
    !updatedData.daYun ||
    !Array.isArray(updatedData.daYun) ||
    updatedData.daYun.length === 0
  ) {
    updatedData.daYun = originalDaYun;
    console.log("🔥 保护大运数组不被覆盖", originalDaYun);
  }

  liupanResult.value = updatedData;

  console.log("🔥 更新后 liupanResult", liupanResult.value);

  // 使用 nextTick 确保 DOM 更新
  await nextTick();
  console.log("🔥 nextTick 完成");
};

// 处理流年更新
const handleLiuNianUpdate = async (newLiupanData: any) => {
  console.log("🔥 父组件收到流年更新数据", newLiupanData);
  console.log("🔥 流年更新前 liupanResult", liupanResult.value);

  // 保存原有的大运数组，防止被覆盖
  const originalDaYun = liupanResult.value?.daYun;

  // 更新流盘结果数据 - 直接替换整个对象来确保响应式更新
  const updatedData = {
    ...liupanResult.value,
    ...newLiupanData
  };

  // 如果新数据中没有大运数组或大运数组为空，保持原有的大运数组
  if (
    !updatedData.daYun ||
    !Array.isArray(updatedData.daYun) ||
    updatedData.daYun.length === 0
  ) {
    updatedData.daYun = originalDaYun;
    console.log("🔥 流年更新时保护大运数组不被覆盖", originalDaYun);
  }

  liupanResult.value = updatedData;

  console.log("🔥 流年更新后 liupanResult", liupanResult.value);

  // 使用 nextTick 确保 DOM 更新
  await nextTick();
  console.log("🔥 流年更新 nextTick 完成");
};

// 处理流月更新
const handleLiuYueUpdate = async (newLiupanData: any) => {
  console.log("🔥 父组件收到流月更新数据", newLiupanData);
  console.log("🔥 流月更新前 liupanResult", liupanResult.value);

  // 保存原有的大运数组，防止被覆盖
  const originalDaYun = liupanResult.value?.daYun;

  // 更新流盘结果数据 - 直接替换整个对象来确保响应式更新
  const updatedData = {
    ...liupanResult.value,
    ...newLiupanData
  };

  // 如果新数据中没有大运数组或大运数组为空，保持原有的大运数组
  if (
    !updatedData.daYun ||
    !Array.isArray(updatedData.daYun) ||
    updatedData.daYun.length === 0
  ) {
    updatedData.daYun = originalDaYun;
    console.log("🔥 流月更新时保护大运数组不被覆盖", originalDaYun);
  }

  liupanResult.value = updatedData;

  console.log("🔥 流月更新后 liupanResult", liupanResult.value);

  // 使用 nextTick 确保 DOM 更新
  await nextTick();
  console.log("🔥 流月更新 nextTick 完成");
};

// 监听Tab切换，当切换到流盘时自动初始化
watch(activeTab, newTab => {
  if (
    newTab === "liupan" &&
    baziResult.value &&
    profileData.value &&
    !liupanResult.value
  ) {
    initializeLiupan();
  }
});

// 跳转到创建页面
const goToCreate = () => {
  router.push({ name: "createPaipan" });
};

onMounted(() => {
  loadBaziResult();
});
</script>

<style scoped>
.bazi-result-page {
  min-height: 100vh;
  background-color: #f8f9fa;
}

/* Tab样式 */
.bazi-tabs {
  background-color: #fff;
}

:deep(.van-tabs__nav) {
  background-color: #fff;
  padding: 0 16px;
}

:deep(.van-tab) {
  font-size: 16px;
  font-weight: 500;
}

:deep(.van-tab--active) {
  color: #8a82f1;
}

:deep(.van-tabs__line) {
  background-color: #8a82f1;
}

/* 内容区域 */
.result-container,
.loading-container,
.error-container,
.empty-container,
.liupan-placeholder {
  padding: 0;
}

.liupan-placeholder {
  padding: 60px 16px;
  text-align: center;
}

.loading-container,
.error-container,
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.action-btn {
  background-color: #8a82f1;
  color: white;
  padding: 12px 24px;
  border: none;
  border-radius: 12px;
  cursor: pointer;
  font-size: 16px;
  margin-top: 20px;
  transition: background-color 0.3s;
  min-width: 120px;
}

.action-btn:hover {
  background-color: #7065d1;
}

.result-container {
  padding: 0 0 20px;
}
</style>
