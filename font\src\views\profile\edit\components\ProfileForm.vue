<template>
  <div class="page-wrapper">
    <div class="form-group">
      <label for="name" class="w-21">姓名</label>
      <input
        id="name"
        v-model="profileForm.profileName"
        type="text"
        placeholder="请输入姓名"
      />
    </div>

    <div class="form-group">
      <button
        class="gender-btn"
        :class="{ active: profileForm.gender === 1 }"
        @click="profileForm.gender = 1"
      >
        男
      </button>
      <button
        class="gender-btn mr-4"
        :class="{ active: profileForm.gender === 2 }"
        @click="profileForm.gender = 2"
      >
        女
      </button>

      <button
        class="calendar-btn"
        :class="{ active: profileForm.profileDateType === 1 }"
        @click="profileDateTypeChangeHandler(1)"
      >
        公历
      </button>
      <button
        class="calendar-btn"
        :class="{ active: profileForm.profileDateType === 2 }"
        @click="profileDateTypeChangeHandler(2)"
      >
        农历
      </button>
      <button
        class="calendar-btn"
        :class="{ active: profileForm.profileDateType === 3 }"
        @click="profileDateTypeChangeHandler(3)"
      >
        四柱
      </button>
    </div>
    <van-popup v-model:show="showFourPillars" position="bottom">
      <DateTimePicker
        :showPicker="showDatePopup"
        title="选择日期时间"
        :values="showDate"
        @cancel="showDatePopup = false"
        @confirm="onConfirmDate"
      />
    </van-popup>

    <div class="form-group">
      <input
        id="datetime"
        type="text"
        :value="showDate"
        readonly
        placeholder="请确认日期时间"
        @click="showDatePopup = true"
      />
      <van-popup v-model:show="showDatePopup" position="bottom">
        <DateTimePicker
          :showPicker="showDatePopup"
          title="选择日期时间"
          :values="showDate"
          @cancel="showDatePopup = false"
          @confirm="onConfirmDate"
        />
      </van-popup>
    </div>

    <div class="form-group location-group">
      <input
        id="location"
        type="text"
        value=""
        readonly
        placeholder="未知地区-刷新定位"
      />
      <span class="location-icon" />
    </div>

    <div class="info-group">
      <p>
        <span class="question-mark">?</span> 真太阳时：{{
          profileForm.realSolarTime
        }}
      </p>
      <p>
        <span class="question-mark">?</span> 经纬度：北纬
        {{ profileForm.latitude }}° 东经 {{ profileForm.longitude }}°
      </p>
    </div>
  </div>
</template>
<script setup lang="ts">
import { SolarTimeUtil } from "@/utils/SolarTimeUtil";
import type { Profile } from "@/views/profile/profile";
import DateTimePicker from "@/components/DateTimePicker/index.vue";
import {
  dateToLunarHour,
  dateToSolarTime,
  formatDate,
  solarTimeToDate
} from "@/utils/DateUtils";

const profileForm = defineModel<Profile>({ required: true });

const showDatePopup = ref(false);
const showDate = ref("");
watch(
  () => profileForm.value.profileDateType,
  newVal => {
    showDate.value = "";
  }
);

const profileDateTypeChangeHandler = (value: number) => {
  profileForm.value.profileDateType = value;
  if (value === 3) {
    showFourPillars.value = true;
  }
};

/**
 * 确认日期
 * 1. 公历 则直接赋值solarBirth
 * 2. 农历 赋值lunarBirth， 并且转化公历赋值solarBirth
 * 3. 四柱 赋值fourPillars， 并且转化公历赋值solarBirth
 * @param value 日期 default format yyyy-MM-DD HH:mm
 */
const onConfirmDate = (value: any) => {
  switch (profileForm.value.profileDateType) {
    case 1:
      profileForm.value.solarBirth = value;
      break;
    case 2:
      const lunarHour = dateToLunarHour(value);
      profileForm.value.solarBirth = formatDate(
        solarTimeToDate(lunarHour.getSolarTime())
      );
      profileForm.value.lunarBirth = value;
      break;
  }
  showDate.value = value;
  showDatePopup.value = false;
  console.log("onConfirmDate", profileForm.value);
};

// 监听 solarBirth、longitude、latitude 的变化
watch(
  () => [
    profileForm.value.solarBirth,
    profileForm.value.longitude,
    profileForm.value.latitude
  ],
  () => {
    if (!profileForm.value.solarBirth) {
      return;
    }
    // 确保 newVal.longitude 和 newVal.latitude 是有效的数字字符串
    const longitude = parseFloat(profileForm.value.longitude);
    const latitude = parseFloat(profileForm.value.latitude);

    if (isNaN(longitude) || isNaN(latitude)) {
      console.error(
        "Invalid longitude or latitude:",
        profileForm.value.longitude,
        profileForm.value.latitude
      );
      return;
    }

    const solarTimeUtil = SolarTimeUtil.initLocation(longitude, latitude);

    const solarTime = dateToSolarTime(profileForm.value.solarBirth);
    const realSolarTime = solarTimeUtil.getRealSolarTime(solarTime);
    profileForm.value.realSolarTime = formatDate(
      solarTimeToDate(realSolarTime)
    );
  },
  { immediate: true, deep: true }
);

// 四柱转公历
const showFourPillars = ref(false);

// List<SolarTime> solarTimes = new EightChar("丁丑", "癸卯", "癸丑", "辛酉").getSolarTimes(1900, 2024);
</script>

<style scoped>
.page-wrapper {
  padding: 17px 6px 0;
}

.form-group {
  margin-bottom: 15px;
  display: flex;
  align-items: center;
}

.form-group label {
  display: inline-block;
  color: #333;
  width: 84px; /* 固定标签宽度 */
  text-align: center;
}

.form-group input[type="text"] {
  flex-grow: 1;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 12px;
  box-sizing: border-box;
  min-width: 0;
}

#name {
  font-family: Roboto;
  font-size: 20px;
  font-weight: normal;
  line-height: 20px;
  letter-spacing: 0px;
  font-feature-settings: "kern" on;
  color: #000000;
}

#name::placeholder {
  align-items: center;
  font-family: Roboto;
  font-size: 14px;
  font-weight: normal;
  letter-spacing: 0px;
  font-feature-settings: "kern" on;
}

.gender-btn {
  width: 84.5px;
  height: 48px;

  border: 1px solid #ddd;
  background-color: #f9f9f9;
  color: #555;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  display: flex; /* 用于垂直居中文本 */
  justify-content: center; /* 水平居中文本 */
  align-items: center; /* 垂直居中文本 */
  box-sizing: border-box; /* 确保 padding 和 border 不会增加总宽度/高度 */
  transition:
    background-color 0.3s,
    color 0.3s,
    border-color 0.3s;
}

.calendar-btn {
  width: 57px;
  height: 48px;

  border: 1px solid #ddd;
  background-color: #f9f9f9;
  color: #555;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  display: flex; /* 用于垂直居中文本 */
  justify-content: center; /* 水平居中文本 */
  align-items: center; /* 垂直居中文本 */
  box-sizing: border-box; /* 确保 padding 和 border 不会增加总宽度/高度 */
  transition:
    background-color 0.3s,
    color 0.3s,
    border-color 0.3s;
}

.gender-btn.active,
.calendar-btn.active {
  background-color: #8a82f1;
  color: white;
  border-color: #8a82f1;
}

#datetime {
  width: 100%; /* 日期时间输入框占满整行 */
  margin-top: 5px; /* 与上一行按钮组的间距 */
  font-size: 16px; /* 保持 #datetime 的原始字体大小 */
}

.location-group {
  position: relative;
}

.location-group input[type="text"] {
  padding-right: 30px; /* 为图标留出空间 */
  font-size: 16px; /* 保持 #location 的原始字体大小 */
}

.location-icon {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  width: 16px;
  height: 16px;
  background-image: url('data:image/svg+xml;charset=UTF-8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 384 512" fill="%238a82f1"><path d="M172.268 501.67C26.97 291.031 0 269.413 0 192 0 85.961 85.961 0 192 0s192 85.961 192 192c0 77.413-26.97 99.031-172.268 309.67a24 24 0 0 1-35.464 0zM192 256a64 64 0 1 0 0-128 64 64 0 0 0 0 128z"/></svg>'); /* 紫色定位图标 SVG */
  background-repeat: no-repeat;
  background-size: contain;
}

.info-group {
  margin-top: 15px;
  font-size: 14px;
  color: #555;
}

.info-group p {
  margin: 5px 0;
  display: flex;
  align-items: center;
}

.question-mark {
  display: inline-block;
  width: 16px;
  height: 16px;
  background-color: #8a82f1;
  color: white;
  border-radius: 50%;
  text-align: center;
  line-height: 16px;
  font-size: 12px;
  margin-right: 8px;
  font-weight: bold;
}
</style>
