# /dev-api 为本地 mock 地址，不使用 mock 的话此处换为你的开发环境接口地址，例如 http://192.168.233.233
VITE_BASE_API = 'http://172.31.73.242:8080'

# 开发环境启用 cdn eruda 调试工具。若不启用，将 true 修改为 false 或其他任意值即可
VITE_ENABLE_ERUDA = "true"

# 线上环境平台打包路径
VITE_PUBLIC_PATH = /

# 文件等前缀修饰
VITE_FILE_PREFIX = https://fund.cdollar.cn/e-learning/courseFile/
# 中台路径
VITE_WEB_URL =  https://fund.cdollar.cn/resources/mcv/bailun-test/#/kng/index
VITE_LOGIN_URL =  https://fund.cdollar.cn/resources/mcv/bailun-test/#/login
