import { http } from "@/utils/http";
import url from "../url";

export default {
  async getProfileList(params: any) {
    return http.request({
      url: url.profile.getProfileList,
      method: "get",
      params
    });
  },
  async getDefaultProfile(params: any) {
    return http.request({
      url: url.profile.getDefaultProfile,
      method: "get",
      params
    });
  },
  async getProfileById(params: any) {
    return http.request({
      url: url.profile.getProfileById,
      method: "get",
      params
    });
  },
  async createProfile(data: any) {
    return http.request({
      url: url.profile.createProfile,
      method: "post",
      data
    });
  },
  async updateProfileById(data: any) {
    return http.request({
      url: url.profile.updateProfileById,
      method: "post",
      data
    });
  },
  async deleteProfileById(data: any) {
    return http.request({
      url: url.profile.deleteProfileById,
      method: "post",
      data
    });
  }
};
