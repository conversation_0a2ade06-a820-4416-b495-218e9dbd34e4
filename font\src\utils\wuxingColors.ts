/**
 * 五行颜色配置
 * 金木水火土对应的颜色定义
 */

// 五行颜色映射
export const WUXING_COLORS = {
  金: "#DAA520", // 金色/黄色
  木: "#228B22", // 绿色
  水: "#1E90FF", // 蓝色
  火: "#DC143C", // 红色
  土: "#8B4513" // 棕色
} as const;

// 天干地支字符到五行的映射表
export const GANZHI_WUXING_MAP = {
  // 天干
  甲: "木",
  乙: "木",
  丙: "火",
  丁: "火",
  戊: "土",
  己: "土",
  庚: "金",
  辛: "金",
  壬: "水",
  癸: "水",
  // 地支
  子: "水",
  丑: "土",
  寅: "木",
  卯: "木",
  辰: "土",
  巳: "火",
  午: "火",
  未: "土",
  申: "金",
  酉: "金",
  戌: "土",
  亥: "水"
} as const;

// 五行类型定义
export type WuXingType = keyof typeof WUXING_COLORS;
export type GanZhiType = keyof typeof GANZHI_WUXING_MAP;

/**
 * 根据天干地支字符获取对应颜色
 * @param ganzhiChar 天干或地支字符，如 "甲"、"子" 等
 * @returns 对应的颜色值
 */
export function getGanZhiColor(ganzhiChar: string): string {
  if (!ganzhiChar) return "#333"; // 默认颜色

  const wuxing = GANZHI_WUXING_MAP[ganzhiChar as GanZhiType];
  if (!wuxing) return "#333";

  return WUXING_COLORS[wuxing] || "#333";
}

/**
 * 获取天干颜色
 * @param tianganChar 天干字符
 * @returns 对应的颜色值
 */
export function getTianGanColor(tianganChar: string): string {
  return getGanZhiColor(tianganChar);
}

/**
 * 获取地支颜色
 * @param dizhiChar 地支字符
 * @returns 对应的颜色值
 */
export function getDiZhiColor(dizhiChar: string): string {
  return getGanZhiColor(dizhiChar);
}

/**
 * 获取藏干颜色
 * @param cangganChar 藏干字符
 * @returns 对应的颜色值
 */
export function getCangGanColor(cangganChar: string): string {
  return getGanZhiColor(cangganChar);
}
