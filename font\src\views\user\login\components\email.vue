<template>
  <div>
    <van-form ref="formRef" @submit="sendVerificationCode">
      <van-field
        v-model="formData.username"
        name="username"
        label="绑定邮箱"
        placeholder="请输入邮箱地址"
        clearable
        label-align="top"
        :rules="[{ pattern: emailRegex, message: '请输入正确的邮箱地址' }]"
      />
      <van-field
        v-model="formData.verificationCode"
        center
        clearable
        label="验证码"
        label-align="top"
        placeholder="请输入验证码"
      >
        <template #button>
          <button
            class="code-btn"
            :disabled="countdown > 0"
            @click="sendVerificationCode"
          >
            {{ countdown > 0 ? `${countdown}s后重新获取` : "获取验证码" }}
          </button>
        </template>
      </van-field>
    </van-form>

    <!-- 可选的说明区域 -->
    <div v-if="showDescription" class="description">
      <p class="mb-2">{{ descriptionTitle }}</p>
      <li v-for="(item, index) in descriptionItems" :key="index">{{ item }}</li>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useData } from "@/hooks/useData";
import api from "@/api/index";

const props = defineProps({
  showDescription: {
    type: Boolean,
    default: false
  },
  descriptionTitle: {
    type: String,
    default: "绑定说明："
  },
  descriptionItems: {
    type: Array,
    default: () => [
      "请确保填写的邮箱地址真实有效",
      "验证码有效期为 5 分钟",
      "如遇问题请联系客服"
    ]
  },
  countdownTime: {
    type: Number,
    default: 60
  }
});

const emit = defineEmits(["update:formData", "validate"]);

const formRef = ref(null);
const formData = reactive({
  username: "",
  verificationCode: ""
});

const countdown = ref(0);
let timer: number | null = null;

const startCountdown = () => {
  countdown.value = props.countdownTime;
  timer = setInterval(() => {
    countdown.value--;
    if (countdown.value <= 0 && timer) {
      clearInterval(timer);
      timer = null;
    }
  }, 1000) as unknown as number;
};

const { error, data, loading, execute, onFetchResponse } = useData(
  api.login.sendCode
);

const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
const sendVerificationCode = async () => {
  if (countdown.value > 0) return;
  const result = await formRef.value
    .validate()
    .then(() => {
      return true;
    })
    .catch(error => {
      return false;
    });

  if (!result) {
    return;
  }
  startCountdown();

  await execute({
    username: formData.username
  });
};

const validate = () => {
  // 邮箱验证
  if (!emailRegex.test(formData.username)) {
    return { valid: false, message: "请输入正确的邮箱地址" };
  }

  // 验证码验证
  if (!formData.verificationCode || formData.verificationCode.trim() === "") {
    return { valid: false, message: "请输入验证码" };
  }

  return { valid: true, data: formData };
};

// 获取表单数据
const getFormData = () => {
  return { ...formData };
};

// 重置表单
const resetForm = () => {
  formData.username = "";
  formData.verificationCode = "";
};

// 向外部暴露方法
defineExpose({
  validate,
  getFormData,
  resetForm,
  formData
});

onUnmounted(() => {
  if (timer) {
    clearInterval(timer);
  }
});
</script>

<style scoped>
.code-btn {
  margin-left: 10px;
  min-width: 110px;
  height: 48px;
  background-color: #7b68ee;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 16px;
  cursor: pointer;
}

.code-btn:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

.description {
  font-size: 14px;
  font-weight: normal;
  line-height: 20px;
  letter-spacing: 0px;
  font-feature-settings: "kern" on;
  color: #6b7280;
}

:deep(.van-cell) {
  padding: 10px 0;
}

:deep(.van-field__control) {
  width: 100%;
  height: 48px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 0 16px;
  font-size: 16px;
  outline: none;
}
</style>
