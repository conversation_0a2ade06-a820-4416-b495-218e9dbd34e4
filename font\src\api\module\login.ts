import { http } from "@/utils/http";
import url from "../url";

export default {
  async login(data: any) {
    return http.request({
      url: url.login.login,
      method: "post",
      data
    });
  },
  async register(data: any) {
    return http.request({
      url: url.login.register,
      method: "post",
      data
    });
  },
  async sendCode(params: any) {
    return http.request({
      url: url.login.sendCode,
      method: "post",
      params
    });
  },
  async info(params: any) {
    return http.request({
      url: url.login.info,
      method: "get",
      params
    });
  },
  async resetPassword(data: any) {
    return http.request({
      url: url.login.resetPassword,
      method: "post",
      data
    });
  }
};
