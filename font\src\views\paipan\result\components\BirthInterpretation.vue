<template>
  <div class="birth-interpretation">
    <!-- 解读卡片 -->
    <div class="interpretation-card">
      <!-- 标题区域 -->
      <div class="interpretation-header">
        <div class="icon-wrapper">
          <div class="book-icon">📖</div>
        </div>
        <h3 class="interpretation-title">生辰解读</h3>
      </div>

      <!-- 加载状态 -->
      <div v-if="loading" class="loading-content">
        <van-loading type="spinner" color="#8a82f1" size="20px"
          >解读生成中...</van-loading
        >
      </div>

      <!-- 错误状态 -->
      <div v-else-if="error" class="error-content">
        <p class="error-text">解读获取失败，请稍后重试</p>
        <button class="retry-btn" @click="loadInterpretation">重新获取</button>
      </div>

      <!-- 解读内容 -->
      <div v-else class="interpretation-content">
        <p class="interpretation-text">
          {{ interpretationText || defaultText }}
        </p>
      </div>

      <!-- 查看更多按钮 -->
      <div v-if="!loading" class="more-action">
        <button class="more-btn" @click="handleMoreClick">
          查看更多解读 <span class="arrow">〉</span>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from "vue";
import { useData } from "@/hooks/useData";
import api from "@/api/index";
import { convertBaziToJson } from "@/utils/baziDataUtils";
import type { Profile } from "@/views/profile/profile";

// 定义props
const props = defineProps<{
  result?: any;
  profile?: Profile;
}>();

// 默认解读文本
const defaultText = "？";

// 解读内容状态
const interpretationText = ref("");

// 使用useData hook调用生辰解读API
const {
  data: interpretationData,
  loading,
  error,
  execute: executeGetInterpretation,
  onFetchResponse: onFetchInterpretationResponse
} = useData(api.paipan.getBirthInterpretation);

// 处理API响应
onFetchInterpretationResponse((response: any) => {
  // 由于HTTP拦截器已经提取了data字段，这里的response就是实际的解读内容
  if (response) {
    if (typeof response === "string") {
      // 如果直接返回字符串（最常见的情况）
      interpretationText.value = response;
    } else if (response.data && typeof response.data === "string") {
      // 如果还有嵌套的data字段
      interpretationText.value = response.data;
    }
  }
});

// 加载解读内容
const loadInterpretation = async () => {
  if (!props.result || !props.profile) {
    return;
  }

  try {
    // 将八字数据转换为JSON字符串
    const baziJsonString = convertBaziToJson(props.result, props.profile);

    // 调用API
    const requestData = {
      profileId: props.profile.profileId || 0,
      baziPaipan: baziJsonString
    };

    await executeGetInterpretation(requestData);
  } catch (err) {
    console.error("获取生辰解读失败:", err);
  }
};

// 处理查看更多点击事件
const handleMoreClick = () => {
  // TODO: 实现跳转到详细解读页面的逻辑
};

// 监听props变化，当数据准备好时自动加载解读
watch(
  () => [props.result, props.profile],
  ([newResult, newProfile]) => {
    if (
      newResult &&
      newProfile &&
      !interpretationText.value &&
      !loading.value
    ) {
      loadInterpretation();
    }
  },
  { immediate: true }
);
</script>

<style scoped lang="less">
.birth-interpretation {
  padding: 0 16px 16px;
}

.interpretation-card {
  background: linear-gradient(135deg, #f8f6ff 0%, #f0f4ff 100%);
  border: 1px solid #e5e7eb;
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0px 4px 12px rgba(138, 130, 241, 0.1);
  position: relative;
  overflow: hidden;

  // 添加微妙的背景装饰
  &::before {
    content: "";
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 100%;
    background: radial-gradient(
      circle,
      rgba(138, 130, 241, 0.05) 0%,
      transparent 70%
    );
    pointer-events: none;
  }
}

.interpretation-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  position: relative;
  z-index: 1;
}

.icon-wrapper {
  margin-right: 12px;
}

.book-icon {
  font-size: 24px;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #8a82f1 0%, #a78bfa 100%);
  border-radius: 12px;
  box-shadow: 0px 2px 8px rgba(138, 130, 241, 0.3);
}

.interpretation-title {
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
  letter-spacing: 0.5px;
}

.interpretation-content {
  position: relative;
  z-index: 1;
  margin-bottom: 20px;
}

.interpretation-text {
  font-size: 15px;
  line-height: 1.7;
  color: #374151;
  margin: 0;
  text-align: justify;
  letter-spacing: 0.3px;
}

.more-action {
  display: flex;
  justify-content: flex-end;
  position: relative;
  z-index: 1;
}

.more-btn {
  background: none;
  border: none;
  color: #8a82f1;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 8px 0;
  transition: all 0.3s ease;

  &:hover {
    color: #7065d1;
    transform: translateX(2px);
  }

  &:active {
    transform: translateX(1px);
  }
}

.arrow {
  font-size: 12px;
  transition: transform 0.3s ease;
}

.more-btn:hover .arrow {
  transform: translateX(2px);
}

// 加载状态样式
.loading-content {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px 20px;
  position: relative;
  z-index: 1;
}

// 错误状态样式
.error-content {
  text-align: center;
  padding: 20px;
  position: relative;
  z-index: 1;
}

.error-text {
  color: #ef4444;
  font-size: 14px;
  margin: 0 0 12px 0;
}

.retry-btn {
  background: #ef4444;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 8px 16px;
  font-size: 12px;
  cursor: pointer;
  transition: background-color 0.3s ease;

  &:hover {
    background: #dc2626;
  }

  &:active {
    transform: translateY(1px);
  }
}
</style>
