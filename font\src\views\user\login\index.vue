<template>
  <div class="h-full flex flex-col gap-4">
    <div class="p-6 flex items-center relative">
      <svg-icon name="go-back" class="absolute left-6" />
      <p class="title mx-auto">{{ $t("user.login.login") }}</p>
    </div>
    <div class="px-6 flex flex-col gap-4">
      <van-form ref="formRef">
        <van-field v-model="formData.username" name="email" :label="$t('user.login.email')"
          :placeholder="$t('user.login.emailPlaceholder')" clearable label-align="top" :rules="[
            { required: true, message: $t('user.login.emailPlaceholder') }
          ]" />
        <van-field v-model="formData.password" name="password" :label="$t('user.login.password')"
          :placeholder="$t('user.login.passwordPlaceholder')" clearable label-align="top" type="password" :rules="[
            { required: true, message: $t('user.login.passwordPlaceholder') }
          ]" />
      </van-form>

      <button class="submit-btn mt-4" @click="handleRegister">
        {{ $t("user.register.top") }}
      </button>
      <button :loading="loading" class="submit-btn mt-4" @click="handleSubmit">
        {{ $t("user.login.login") }}
      </button>
      <p class="agree">{{ $t("user.login.agree") }}</p>
    </div>
  </div>
</template>
<script setup lang="ts">
import { useData } from "@/hooks/useData";
import api from "@/api/index";
import { useUserStore } from "@/store/modules/user";
const router = useRouter();
const userStore = useUserStore();

const formData = reactive({
  username: "",
  password: ""
});

const formRef = ref(null);

const {
  loading,
  execute: login,
  onFetchResponse: loginResponse,
  onFetchError: loginError
} = useData(api.login.login);

const {
  execute: getUserInfo,
  onFetchResponse: getUserInfoResponse,
  onFetchError: getUserInfoError
} = useData(api.login.info);

loginResponse(params => {
  localStorage.setItem("Authorization", params.tokenHead + params.token);
  getUserInfo({});
});

getUserInfoResponse(params => {
  userStore.setUserInfo(params);
  router.push("/home");
});

// 登录错误处理
loginError(error => {
  console.error("登录失败:", error);
});

// 获取用户信息错误处理
getUserInfoError(error => {
  console.error("获取用户信息失败:", error);
  // 清除可能无效的token
  localStorage.removeItem("Authorization");
});

const handleSubmit = async () => {
  let formValid = false;
  try {
    await formRef.value.validate();
    formValid = true;
  } catch (errors) {
    console.error("表单校验失败：", errors);
  }

  if (!formValid) {
    return;
  }
  await login(formData);

  // console.log("token", localStorage.getItem("Authorization"));
  // const { userInfo } = storeToRefs(userStore);
  // console.log("用户信息", userInfo.value);
};

const handleRegister = () => {
  router.push("/register");
};
</script>
<style scoped>
.title {
  width: 80px;
  height: 28px;
  opacity: 1;

  font-family: Roboto;
  font-size: 20px;
  font-weight: 500;
  line-height: 28px;
  text-align: center;
  letter-spacing: 0px;

  font-feature-settings: "kern" on;
  color: #000000;

  z-index: 2;
}

.submit-btn {
  width: 100%;
  height: 48px;
  background-color: #7b68ee;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 16px;
  cursor: pointer;
}

.description {
  font-size: 14px;
  font-weight: normal;
  line-height: 20px;
  letter-spacing: 0px;

  font-feature-settings: "kern" on;
  color: #6b7280;
}

.agree {
  font-family: Roboto;
  font-size: 12px;
  font-weight: normal;
  line-height: 16px;
  text-align: center;
  letter-spacing: 0px;

  font-feature-settings: "kern" on;
  color: #9ca3af;
}

:deep(.van-cell) {
  padding: 10px 0;
}

:deep(.van-field__control) {
  width: 100%;
  height: 48px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 0 16px;
  font-size: 16px;
  outline: none;
}
</style>
