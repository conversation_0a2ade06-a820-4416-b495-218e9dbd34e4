<template>
  <div class="p-4 flex">
    <svg-icon name="avatar" class-name="avatar" />
    <div class="ml-3">
      <p class="title">{{ row.profileName }}</p>
      <div class="flex justify-center items-center content">
        <p v-if="row.isDefault" class="mr-1">{{ $t("system.default") }}</p>
        <svg-icon
          v-if="row.gender === 1"
          name="male"
          class-name="gender-icon"
        />
        <svg-icon v-else name="female" class-name="gender-icon" />
        <p class="ml-1">{{ formatDate(row.solarBirth, "YYYY.MM.DD") }}</p>
      </div>
    </div>
    <div class="ml-auto flex flex-col justify-between">
      <svg-icon
        name="message-recycle"
        class-name="recycle-icon"
        @click="showDialog = true"
      />
      <svg-icon name="edit" class-name="edit-icon" @click="editProfile" />
    </div>
    <van-dialog
      v-model:show="showDialog"
      :title="$t('system.tip')"
      :message="`确认删除此档案吗？`"
      show-cancel-button
      :cancel-button-text="$t('system.cancel')"
      :confirm-button-text="$t('system.confirm')"
      @confirm="deleteProfile"
      @cancel="cancelDelete"
    />
  </div>
</template>
<script setup lang="ts">
import { Profile } from "@/views/profile/profile";
import { formatDate } from "@/utils/DateUtils";
const props = defineProps<{
  row: Profile;
}>();

const emits = defineEmits(["deleteProfile"]);

const router = useRouter();
const editProfile = () => {
  router.push({
    path: "/profile/edit",
    query: { id: props.row.profileId }
  });
};

const showDialog = ref(false);

const deleteProfile = () => {
  emits("deleteProfile", props.row.profileId);
  cancelDelete();
};

const cancelDelete = () => {
  showDialog.value = false;
};
</script>

<style scoped>
.title {
  font-family: Roboto;
  font-size: 16px;
  font-weight: 500;
  line-height: 24px;
  letter-spacing: 0px;

  font-feature-settings: "kern" on;
  color: #000000;
}

.content {
  font-family: Roboto;
  font-size: 14px;
  font-weight: normal;
  line-height: 20px;
  letter-spacing: 0px;

  font-feature-settings: "kern" on;
  color: #6b7280;
}

.avatar {
  width: 48px;
  height: 48px;
}

.recycle-icon {
  color: #9ca3af;
}

.gender-icon {
  width: 16px;
  height: 16px;
}
</style>
