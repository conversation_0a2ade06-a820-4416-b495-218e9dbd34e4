<template>
  <van-tabbar
    v-model="active"
    :route="true"
    :placeholder="true"
    :fixed="true"
    active-color="#92ABFF"
    inactive-color="#757575"
  >
    <van-tabbar-item
      v-for="(item, index) in tabbarData"
      :key="index"
      :to="item.to"
    >
      <template #icon="props">
        <svg-icon :name="item.icon" />
      </template>
      {{ item.title }}
    </van-tabbar-item>
  </van-tabbar>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue";

const active = ref(0);
const tabbarData = reactive([
  {
    icon: "home",
    title: "首页",
    to: {
      name: "Home"
    }
  },
  {
    icon: "message",
    title: "消息",
    to: {
      name: "Message"
    }
  },
  {
    icon: "user",
    title: "我的",
    to: {
      name: "User"
    }
  }
]);
</script>

<style scoped></style>
