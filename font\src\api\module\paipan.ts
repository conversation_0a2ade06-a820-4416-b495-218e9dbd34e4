import { http } from "@/utils/http";
import url from "../url";

export default {
  async paipanStarts(data: any) {
    return http.request({
      url: url.paipan.paipanStarts,
      method: "post",
      data
    });
  },
  async daYunLiuNianInitialize(data: any) {
    return http.request({
      url: url.paipan.daYunLiuNianInitialize,
      method: "post",
      data
    });
  },
  async daYunUpdate(data: any) {
    return http.request({
      url: url.paipan.daYunUpdate,
      method: "post",
      data
    });
  },
  async liuNianUpdate(data: any) {
    return http.request({
      url: url.paipan.liuNianUpdate,
      method: "post",
      data
    });
  },
  async liuYueUpdate(data: any) {
    return http.request({
      url: url.paipan.liuYueUpdate,
      method: "post",
      data
    });
  },
  async getBirthInterpretation(data: any) {
    return http.request({
      url: url.paipan.birthInterpretation,
      method: "post",
      data
    });
  }
};
