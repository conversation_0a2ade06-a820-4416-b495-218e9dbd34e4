<template>
  <div class="h-full flex flex-col gap-4 userBox">
    <top />
    <div class="px-6 flex flex-col gap-4">
      <user-info />
      <vip-operator />
      <user-function />
    </div>
  </div>
</template>
<script setup lang="ts">
import top from "./top.vue";
import userInfo from "./main/userInfo.vue";
import vipOperator from "./main/vipOperator.vue";
import UserFunction from "./main/userFunction.vue";
</script>
<style scoped>
.userBox {
  background:
    linear-gradient(0deg, rgba(0, 0, 0, 0), rgba(0, 0, 0, 0)),
    linear-gradient(132deg, #e0f2fe -1%, #dbeafe 101%);
}
</style>
