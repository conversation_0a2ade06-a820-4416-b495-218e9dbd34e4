/**
 * 八字数据处理工具函数
 * 用于从八字排盘结果中提取各种数据
 */

// 获取天干 - 从baZi字段中提取
export const getTianGan = (result: any, index: number): string => {
  if (
    !result?.baZi ||
    !Array.isArray(result.baZi) ||
    index >= result.baZi.length
  ) {
    return "";
  }
  // baZi[index] 是类似 "乙巳" 的字符串，取第一个字符作为天干
  const ganZhi = result.baZi[index];

  if (typeof ganZhi === "string" && ganZhi.length >= 2) {
    return ganZhi.charAt(0); // 第一个字符是天干，如 "乙"
  }
  return "";
};

// 获取地支 - 从baZi字段中提取
export const getDiZhi = (result: any, index: number): string => {
  if (
    !result?.baZi ||
    !Array.isArray(result.baZi) ||
    index >= result.baZi.length
  ) {
    return "";
  }
  // baZi[index] 是类似 "乙巳" 的字符串，取第二个字符作为地支
  const ganZhi = result.baZi[index];

  if (typeof ganZhi === "string" && ganZhi.length >= 2) {
    return ganZhi.charAt(1); // 第二个字符是地支，如 "巳"
  }
  return "";
};

// 获取主星（干神）
export const getZhuXing = (result: any, index: number): string => {
  const fields = [
    "yearGanZhiZhuXing",
    "monthGanZhiZhuXing",
    "dayGanZhiZhuXing",
    "hourGanZhiZhuXing"
  ];
  return result?.[fields[index]] || "";
};

// 获取藏干
export const getCangGan = (result: any, index: number): string[] => {
  const fields = [
    "yearZhiCangGan",
    "monthZhiCangGan",
    "dayZhiCangGan",
    "hourZhiCangGan"
  ];
  const canggan = result?.[fields[index]];
  return Array.isArray(canggan) ? canggan : [];
};

// 获取福星（支神）
export const getFuXing = (result: any, index: number): string[] => {
  const fields = [
    "yearGanZhiFuXing",
    "monthGanZhiFuXing",
    "dayGanZhiFuXing",
    "hourGanZhiFuXing"
  ];
  const fuxing = result?.[fields[index]];
  return Array.isArray(fuxing) ? fuxing : [];
};

// 获取纳音
export const getNaYin = (result: any, index: number): string => {
  const fields = [
    "yearGanZhiNaYin",
    "monthGanZhiNaYin",
    "dayGanZhiNaYin",
    "hourGanZhiNaYin"
  ];
  return result?.[fields[index]] || "";
};

// 获取空亡
export const getKongWang = (result: any, index: number): string => {
  const fields = [
    "yearGanZhiKongWang",
    "monthGanZhiKongWang",
    "dayGanZhiKongWang",
    "hourGanZhiKongWang"
  ];
  return result?.[fields[index]] || "";
};

// 获取地势
export const getDiShi = (result: any, index: number): string => {
  const fields = [
    "yearGanZhiDiShi",
    "monthGanZhiDiShi",
    "dayGanZhiDiShi",
    "hourGanZhiDiShi"
  ];
  return result?.[fields[index]] || "";
};

// 获取神煞
export const getShenSha = (result: any, index: number): string[] => {
  const fields = [
    "yearGanZhiShenSha",
    "monthGanZhiShenSha",
    "dayGanZhiShenSha",
    "hourGanZhiShenSha"
  ];
  const shensha = result?.[fields[index]];
  return Array.isArray(shensha) ? shensha : [];
};

// 流盘专用：获取大运数据
export const getDaYunData = (liupanResult: any) => {
  if (!liupanResult)
    return { tianGan: "", diZhi: "", cangGan: [], diShi: "", zhuXing: "" };

  console.log("🔥 getDaYunData 输入", {
    daYunGanZhi: liupanResult.daYunGanZhi,
    daYunZhuXing: liupanResult.daYunZhuXing,
    daYunCangGan: liupanResult.daYunCangGan,
    daYunDiShi: liupanResult.daYunDiShi
  });

  const result = {
    tianGan: liupanResult.daYunGanZhi?.charAt(0) || "", // 天干从干支第一个字符获取
    diZhi: liupanResult.daYunGanZhi?.charAt(1) || "", // 地支从干支第二个字符获取
    cangGan: Array.isArray(liupanResult.daYunCangGan)
      ? liupanResult.daYunCangGan
      : [],
    diShi: liupanResult.daYunDiShi || "",
    zhuXing: liupanResult.daYunZhuXing || ""
  };

  console.log("🔥 getDaYunData 输出", result);
  return result;
};

// 流盘专用：获取流年数据
export const getLiuNianData = (liupanResult: any) => {
  if (!liupanResult)
    return { tianGan: "", diZhi: "", cangGan: [], diShi: "", zhuXing: "" };

  return {
    tianGan: liupanResult.liuNianGanZhi?.charAt(0) || "", // 天干从干支第一个字符获取
    diZhi: liupanResult.liuNianGanZhi?.charAt(1) || "", // 地支从干支第二个字符获取
    cangGan: Array.isArray(liupanResult.liuNianCangGan)
      ? liupanResult.liuNianCangGan
      : [],
    diShi: liupanResult.liuNianDiShi || "",
    zhuXing: liupanResult.liuNianZhuXing || ""
  };
};

// 流盘专用：获取流月数据
export const getLiuYueData = (liupanResult: any) => {
  if (!liupanResult)
    return { tianGan: "", diZhi: "", cangGan: [], diShi: "", zhuXing: "" };

  // 流月数据在初始化时通常为空，只有在选择具体流月后才有数据
  return {
    tianGan: "",
    diZhi: "",
    cangGan: [],
    diShi: "",
    zhuXing: ""
  };
};

// 将八字数据转换为JSON字符串，用于生辰解读API
export const convertBaziToJson = (result: any, profile: any): string => {
  if (!result || !profile) {
    return JSON.stringify({});
  }

  // 构建八字数据结构
  const baziData = {
    // 基本信息
    profileInfo: {
      name: profile.profileName || "",
      gender: profile.gender === 1 ? "男" : "女",
      birthDate: profile.realSolarTime || "",
      birthType:
        profile.profileDateType === 1
          ? "公历"
          : profile.profileDateType === 2
            ? "农历"
            : "四柱",
      location: {
        longitude: profile.longitude || "",
        latitude: profile.latitude || ""
      }
    },

    // 八字四柱
    fourPillars: {
      year: {
        ganZhi: result.baZi?.[0] || "",
        tianGan: getTianGan(result, 0),
        diZhi: getDiZhi(result, 0),
        zhuXing: getZhuXing(result, 0),
        cangGan: getCangGan(result, 0),
        fuXing: getFuXing(result, 0),
        naYin: getNaYin(result, 0),
        kongWang: getKongWang(result, 0),
        diShi: getDiShi(result, 0),
        shenSha: getShenSha(result, 0)
      },
      month: {
        ganZhi: result.baZi?.[1] || "",
        tianGan: getTianGan(result, 1),
        diZhi: getDiZhi(result, 1),
        zhuXing: getZhuXing(result, 1),
        cangGan: getCangGan(result, 1),
        fuXing: getFuXing(result, 1),
        naYin: getNaYin(result, 1),
        kongWang: getKongWang(result, 1),
        diShi: getDiShi(result, 1),
        shenSha: getShenSha(result, 1)
      },
      day: {
        ganZhi: result.baZi?.[2] || "",
        tianGan: getTianGan(result, 2),
        diZhi: getDiZhi(result, 2),
        zhuXing: getZhuXing(result, 2),
        cangGan: getCangGan(result, 2),
        fuXing: getFuXing(result, 2),
        naYin: getNaYin(result, 2),
        kongWang: getKongWang(result, 2),
        diShi: getDiShi(result, 2),
        shenSha: getShenSha(result, 2)
      },
      hour: {
        ganZhi: result.baZi?.[3] || "",
        tianGan: getTianGan(result, 3),
        diZhi: getDiZhi(result, 3),
        zhuXing: getZhuXing(result, 3),
        cangGan: getCangGan(result, 3),
        fuXing: getFuXing(result, 3),
        naYin: getNaYin(result, 3),
        kongWang: getKongWang(result, 3),
        diShi: getDiShi(result, 3),
        shenSha: getShenSha(result, 3)
      }
    },

    // 其他信息
    additionalInfo: {
      season: result.season || "",
      birthSolarTerms: result.birthSolarTerms || "",
      baZiWuXing: result.baZiWuXing || []
    }
  };

  return JSON.stringify(baziData, null, 2);
};
