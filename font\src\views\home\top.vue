<template>
  <div class="flex justify-between items-center py-2">
    <span>logo1</span>
    <input class="bg-white rounded-2xl py-1 px-4" placeholder="搜索你想要的内容" />
    <!-- VIP状态显示 -->
    <div class="vip-status">
      <!-- VIP用户显示金色VIP图标 -->
      <svg-icon v-if="isVipUser" name="vip" class-name="vip-icon-active" />
      <!-- 普通用户显示灰色VIP图标 -->
      <svg-icon v-else name="vip" class-name="vip-icon-normal" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { useUserStore } from "@/store/modules/user";

const store = useUserStore();
const { userInfo } = storeToRefs(store);

// 基于roles数组判断是否为VIP用户
const isVipUser = computed(() => {
  return userInfo.value.roles?.includes("VIP用户") || false;
});
</script>

<style scoped>
.vip-status {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 8px;
}

:deep(.vip-icon-active) {
  width: 24px;
  height: 24px;
  color: #fbbf24;
  /* 金色 */
}

:deep(.vip-icon-normal) {
  width: 24px;
  height: 24px;
  color: #9ca3af;
  /* 灰色 */
}
</style>
