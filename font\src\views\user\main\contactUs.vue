<template>
  <div class="h-full flex flex-col">
    <div class="nav-top">
      <van-icon
        name="arrow-left"
        class="text-white nav-back-icon"
        @click="goBack"
      />
      <p class="nav-title">{{ $t("user.contactUs.top") }}</p>
    </div>
    <div class="page-wrap">
      <div class="contact-us flex flex-col">
        <p class="title mb-5 text-center">
          {{ $t("user.contactUs.title") }}
        </p>
        <p class="content text-center mb-5">
          {{ $t("user.contactUs.content") }}
        </p>
        <div class="ml-[27px]">
          <!-- <p class="content">工作时间</p> -->
          <p class="content">{{ $t("user.contactUs.workTime") }}</p>
          <p class="content mb-4">周一至周五 9:00-18:00</p>
          <p class="content">{{ $t("user.contactUs.phoneNum") }}</p>
          <p class="content mb-4">************</p>
          <p class="content">{{ $t("user.contactUs.wechatNum") }}</p>
          <p class="content">support_2024</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const router = useRouter();
const goBack = () => {
  router.back();
};
</script>
<style scoped>
.nav-top {
  width: 100%;
  min-height: 56px;

  /* 自动布局 */
  display: flex;
  align-items: center;
  align-content: center;
  justify-content: center;

  position: sticky;
  top: 0;
  z-index: 1000;

  background: #92abff;
}

.nav-title {
  font-family: Roboto;
  font-size: 18px;
  font-weight: bold;
  line-height: 27px;
  letter-spacing: 0px;

  font-variation-settings: "opsz" auto;
  font-feature-settings: "kern" on;
  color: #ffffff;
}

.nav-back-icon {
  position: absolute;
  left: 8px;
}

.page-wrap {
  flex: 1;
  background: #dff0fe;
  padding: 8px;
}

.contact-us {
  height: 328px;

  background: #ffffff;
  border-radius: 12px;
  padding: 24px 0;
  margin-top: 131px;
}

.title {
  font-family: Roboto;
  font-size: 20px;
  font-weight: 500;
  line-height: 28px;
  letter-spacing: 0px;

  font-feature-settings: "kern" on;
  color: #000000;

  z-index: 3;
}

.content {
  font-family: Roboto;
  font-size: 16px;
  font-weight: normal;
  line-height: 24px;
  letter-spacing: 0px;

  font-feature-settings: "kern" on;
  color: #4b5563;
}
</style>
