import { http } from "@/utils/http";
import url from "../url";

export default {
  async myDraft(params: any) {
    return http.request({
      url: url.discussion.myDraft,
      method: "get",
      params
    });
  },
  async myDiscussion(params: any) {
    return http.request({
      url: url.discussion.myDiscussion,
      method: "get",
      params
    });
  },
  async deleteBatchDiscussion(data: any) {
    return http.request({
      url: url.discussion.deleteBatchDiscussion,
      method: "post",
      data
    });
  },
  async createDiscussion(data: any) {
    return http.request({
      url: url.discussion.createDiscussion,
      method: "post",
      data
    });
  },
  async publishDiscussion(data: any) {
    return http.request({
      url: url.discussion.publishDiscussion,
      method: "post",
      data
    });
  },
  async getDiscussionDetail(params: any) {
    return http.request({
      url: url.discussion.getDiscussionDetail,
      method: "get",
      params
    });
  }
};
