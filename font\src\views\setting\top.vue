<template>
  <div class="nav-top">
    <van-icon name="arrow-left" class="white" @click="toUser" />
    <p class="nav-title">{{ $t("setting.top") }}</p>
  </div>
</template>

<script setup lang="ts">
const router = useRouter();
const toUser = () => {
  router.push("/user");
};
</script>
<style scoped>
.nav-top {
  width: 100%;
  height: 56px;

  display: flex;
  align-items: center;
  padding: 0 16px;
  flex-wrap: wrap;
  align-content: center;

  background: #92abff;

  box-sizing: border-box;
  border-width: 0 0 1px 0;
  border-style: solid;
  border-color: #f3f4f6;
}

.nav-title {
  /* 自动布局子元素 */
  position: absolute;
  left: 170px;
  top: 14px;
  width: 36px;
  height: 28px;
  opacity: 1;

  font-family: Roboto;
  font-size: 18px;
  font-weight: 500;
  line-height: 28px;
  letter-spacing: 0px;

  font-feature-settings: "kern" on;
  color: #ffffff;
}

.white {
  color: #ffffff;
}
</style>
