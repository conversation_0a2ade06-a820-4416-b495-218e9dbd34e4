<template>
  <div class="box">
    <div class="w-full card-title">{{ $t("setting.setting") }}</div>
    <div class="box-item" @click="toggleMenu">
      <svg-icon name="language-setting" />
      <p class="item-font">{{ $t("setting.language") }}</p>
      <div class="flex items-center ml-auto">
        <p class="placeholder mr-2">{{ language }}</p>
        <van-icon name="arrow" class="placeholder" />
      </div>
    </div>
    <van-popup
      v-model:show="showMenu"
      position="bottom"
      :style="{ height: '30%' }"
    >
      <van-cell
        v-for="item in languageOptions"
        :key="item.value"
        :title="item.label"
        @click="selectLanguage(item.value)"
      >
        <van-icon v-if="item.value === languageCode" name="success" />
      </van-cell>
    </van-popup>
  </div>
</template>
<script setup lang="ts">
import { changeLanguage } from "@/locales";

// 语言菜单
const showMenu = ref(false);
const languageOptions = [
  { value: "zh-CN", label: "简体中文" },
  { value: "en-US", label: "English" }
];

const languageCode = ref(localStorage.getItem("language") || "zh_CN");

const language = ref(
  languageOptions.find(item => item.value === languageCode.value)?.label ||
    "简体中文"
);

const toggleMenu = () => {
  showMenu.value = !showMenu.value;
};

// 监听菜单visible
watch(
  () => showMenu.value,
  newVal => {
    languageCode.value = localStorage.getItem("language") || "zh_CN";
    language.value =
      languageOptions.find(item => item.value === languageCode.value)?.label ||
      "简体中文";
  }
);

const selectLanguage = (selected: string) => {
  changeLanguage(selected);
  localStorage.setItem("language", selected);
  showMenu.value = false;
};
</script>
<style scoped>
.box {
  /* 自动布局子元素 */
  width: 343px;
  padding: 16px 16px 30px 16px;
  border-radius: 12px;
  opacity: 1;

  background:
    linear-gradient(0deg, rgba(0, 0, 0, 0.001), rgba(0, 0, 0, 0.001)), #ffffff;

  box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.05);
}

.card-title {
  font-family: Roboto;
  font-size: 16px;
  font-weight: 500;
  line-height: 24px;
  letter-spacing: 0px;
  padding-bottom: 8px;

  font-feature-settings: "kern" on;
  color: #000000;
}

.box-item {
  /* 自动布局 */
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 20px 0;
  flex-wrap: wrap;
  align-content: center;

  background: rgba(0, 0, 0, 0);

  z-index: 2;

  border-top: 1px solid #e2e8f0;
}

.item-font {
  padding-left: 12px;

  font-family: Roboto;
  font-size: 16px;
  font-weight: normal;
  line-height: 24px;
  letter-spacing: 0px;

  font-feature-settings: "kern" on;
  color: #000000;
}

.placeholder {
  color: #9ca3af;
}
</style>
