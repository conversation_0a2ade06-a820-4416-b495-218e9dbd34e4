<template>
  <div class="liupan-result-display">
    <!-- 流盘表格容器 - 两个表格紧贴在一起 -->
    <div class="liupan-tables-container">
      <!-- 第一个表格：主要流盘信息 -->
      <div class="liupan-table-container">
        <div class="liupan-table">
          <!-- 表头 -->
          <div class="table-header">
            <div class="header-cell category-header" />
            <div class="header-cell">年柱</div>
            <div class="header-cell">月柱</div>
            <div class="header-cell">日柱</div>
            <div class="header-cell">时柱</div>
            <div class="header-cell">大运</div>
            <div class="header-cell">流年</div>
            <div class="header-cell">流月</div>
          </div>

          <!-- 主星行 -->
          <div class="table-row">
            <div class="row-cell category-cell">主星</div>
            <div
              v-for="index in 4"
              :key="`zhuxing-${index - 1}`"
              class="row-cell"
            >
              {{ getZhuXing(baziResult, index - 1) || "-" }}
            </div>
            <div class="row-cell">{{ daYunData.zhuXing || "-" }}</div>
            <div class="row-cell">{{ liuNianData.zhuXing || "-" }}</div>
            <div class="row-cell">{{ liuYueData.zhuXing || "-" }}</div>
          </div>

          <!-- 天干行 -->
          <div class="table-row">
            <div class="row-cell category-cell">天干</div>
            <div
              v-for="index in 4"
              :key="`tiangan-${index - 1}`"
              class="row-cell tiangan-cell"
            >
              {{ getTianGan(baziResult, index - 1) || "-" }}
            </div>
            <div class="row-cell tiangan-cell">
              {{ daYunData.tianGan || "-" }}
            </div>
            <div class="row-cell tiangan-cell">
              {{ liuNianData.tianGan || "-" }}
            </div>
            <div class="row-cell tiangan-cell">
              {{ liuYueData.tianGan || "-" }}
            </div>
          </div>

          <!-- 地支行 -->
          <div class="table-row">
            <div class="row-cell category-cell">地支</div>
            <div
              v-for="index in 4"
              :key="`dizhi-${index - 1}`"
              class="row-cell dizhi-cell"
            >
              {{ getDiZhi(baziResult, index - 1) || "-" }}
            </div>
            <div class="row-cell dizhi-cell">{{ daYunData.diZhi || "-" }}</div>
            <div class="row-cell dizhi-cell">
              {{ liuNianData.diZhi || "-" }}
            </div>
            <div class="row-cell dizhi-cell">{{ liuYueData.diZhi || "-" }}</div>
          </div>

          <!-- 藏干行 -->
          <div class="table-row">
            <div class="row-cell category-cell">藏干</div>
            <div
              v-for="index in 4"
              :key="`canggan-${index - 1}`"
              class="row-cell canggan-cell"
            >
              <div class="canggan-content">
                <div
                  v-if="getCangGan(baziResult, index - 1).length === 0"
                  class="empty-placeholder"
                >
                  -
                </div>
                <div
                  v-for="(canggan, cgIndex) in getCangGan(
                    baziResult,
                    index - 1
                  )"
                  v-else
                  :key="cgIndex"
                  class="canggan-item"
                >
                  {{ canggan }}
                </div>
              </div>
            </div>
            <div class="row-cell canggan-cell">
              <div class="canggan-content">
                <div
                  v-if="daYunData.cangGan.length === 0"
                  class="empty-placeholder"
                >
                  -
                </div>
                <div
                  v-for="(canggan, cgIndex) in daYunData.cangGan"
                  v-else
                  :key="cgIndex"
                  class="canggan-item"
                >
                  {{ canggan }}
                </div>
              </div>
            </div>
            <div class="row-cell canggan-cell">
              <div class="canggan-content">
                <div
                  v-if="liuNianData.cangGan.length === 0"
                  class="empty-placeholder"
                >
                  -
                </div>
                <div
                  v-for="(canggan, cgIndex) in liuNianData.cangGan"
                  v-else
                  :key="cgIndex"
                  class="canggan-item"
                >
                  {{ canggan }}
                </div>
              </div>
            </div>
            <div class="row-cell canggan-cell">
              <div class="canggan-content">
                <div
                  v-if="liuYueData.cangGan.length === 0"
                  class="empty-placeholder"
                >
                  -
                </div>
                <div
                  v-for="(canggan, cgIndex) in liuYueData.cangGan"
                  v-else
                  :key="cgIndex"
                  class="canggan-item"
                >
                  {{ canggan }}
                </div>
              </div>
            </div>
          </div>

          <!-- 地势行 -->
          <div class="table-row">
            <div class="row-cell category-cell">地势</div>
            <div
              v-for="index in 4"
              :key="`dishi-${index - 1}`"
              class="row-cell"
            >
              {{ getDiShi(baziResult, index - 1) || "-" }}
            </div>
            <div class="row-cell">{{ daYunData.diShi || "-" }}</div>
            <div class="row-cell">{{ liuNianData.diShi || "-" }}</div>
            <div class="row-cell">{{ liuYueData.diShi || "-" }}</div>
          </div>
        </div>
      </div>

      <!-- 第二个表格：大运、流年、流月详细信息 -->
      <div class="liupan-detail-card">
        <div class="card-header">
          <h3>大运流年流月详情</h3>
        </div>

        <!-- 大运部分 -->
        <div class="dayun-section">
          <div class="section-title">大运</div>
          <div class="dayun-table">
            <!-- 年龄行 -->
            <div class="dayun-row age-row">
              <div
                v-for="(item, index) in daYunList"
                :key="`age-${index}`"
                :class="[
                  'dayun-cell',
                  'age-cell',
                  { selected: selectedDaYunIndex === index }
                ]"
                @click="selectDaYun(index)"
              >
                {{ getAgeDisplay(item, index) }}
              </div>
            </div>
            <!-- 年份行 -->
            <div class="dayun-row year-row">
              <div
                v-for="(item, index) in daYunList"
                :key="`year-${index}`"
                :class="[
                  'dayun-cell',
                  'year-cell',
                  { selected: selectedDaYunIndex === index }
                ]"
                @click="selectDaYun(index)"
              >
                {{ item.year }}
              </div>
            </div>
            <!-- 干支+十神行（左右布局） -->
            <div class="dayun-row ganzhi-shishen-row">
              <div
                v-for="(item, index) in daYunList"
                :key="`combined-${index}`"
                :class="[
                  'dayun-cell',
                  'combined-cell',
                  { selected: selectedDaYunIndex === index }
                ]"
                @click="selectDaYun(index)"
              >
                <div class="combined-content">
                  <!-- 左侧：干支（竖向） -->
                  <div class="ganzhi-part">
                    <div class="gan">
                      {{ item.ganZhi ? item.ganZhi[0] : "" }}
                    </div>
                    <div class="zhi">
                      {{ item.ganZhi ? item.ganZhi[1] : "" }}
                    </div>
                  </div>
                  <!-- 右侧：十神（竖向） -->
                  <div class="shishen-part">
                    <div class="shishen1">
                      {{ item.shiShen1 ? item.shiShen1[0] : "" }}
                    </div>
                    <div class="shishen2">
                      {{ item.shiShen2 ? item.shiShen2[0] : "" }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 流年部分 -->
        <div class="liunian-section">
          <div class="section-title">流年</div>
          <div class="liunian-table">
            <!-- 年龄行 -->
            <div class="liunian-row age-row">
              <div
                v-for="(item, index) in liuNianList"
                :key="`age-${index}`"
                :class="[
                  'liunian-cell',
                  'age-cell',
                  { selected: selectedLiuNianIndex === index }
                ]"
                @click="selectLiuNian(index)"
              >
                {{ item.age }}岁
              </div>
            </div>
            <!-- 年份行 -->
            <div class="liunian-row year-row">
              <div
                v-for="(item, index) in liuNianList"
                :key="`year-${index}`"
                :class="[
                  'liunian-cell',
                  'year-cell',
                  { selected: selectedLiuNianIndex === index }
                ]"
                @click="selectLiuNian(index)"
              >
                {{ item.year }}
              </div>
            </div>
            <!-- 干支+十神行（左右布局） -->
            <div class="liunian-row ganzhi-shishen-row">
              <div
                v-for="(item, index) in liuNianList"
                :key="`combined-${index}`"
                :class="[
                  'liunian-cell',
                  'combined-cell',
                  { selected: selectedLiuNianIndex === index }
                ]"
                @click="selectLiuNian(index)"
              >
                <div class="combined-content">
                  <!-- 左侧：干支（竖向） -->
                  <div class="ganzhi-part">
                    <div class="gan">
                      {{ item.ganZhi ? item.ganZhi[0] : "" }}
                    </div>
                    <div class="zhi">
                      {{ item.ganZhi ? item.ganZhi[1] : "" }}
                    </div>
                  </div>
                  <!-- 右侧：十神（竖向） -->
                  <div class="shishen-part">
                    <div class="shishen1">
                      {{ item.shiShen1 ? item.shiShen1[0] : "" }}
                    </div>
                    <div class="shishen2">
                      {{ item.shiShen2 ? item.shiShen2[0] : "" }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 流月部分 -->
        <div class="liuyue-section">
          <div class="section-title">流月</div>
          <div class="liuyue-table">
            <!-- 月份名称行 -->
            <div class="liuyue-row month-name-row">
              <div
                v-for="(item, index) in liuYueList"
                :key="`month-${index}`"
                :class="[
                  'liuyue-cell',
                  'month-name-cell',
                  { selected: selectedLiuYueIndex === index }
                ]"
                @click="selectLiuYue(index)"
              >
                {{ item.monthName }}
              </div>
            </div>
            <!-- 日期行 -->
            <div class="liuyue-row date-row">
              <div
                v-for="(item, index) in liuYueList"
                :key="`date-${index}`"
                :class="[
                  'liuyue-cell',
                  'date-cell',
                  { selected: selectedLiuYueIndex === index }
                ]"
                @click="selectLiuYue(index)"
              >
                {{ item.monthDisplay }}
              </div>
            </div>
            <!-- 干支+十神行（左右布局） -->
            <div class="liuyue-row ganzhi-shishen-row">
              <div
                v-for="(item, index) in liuYueList"
                :key="`combined-${index}`"
                :class="[
                  'liuyue-cell',
                  'combined-cell',
                  { selected: selectedLiuYueIndex === index }
                ]"
                @click="selectLiuYue(index)"
              >
                <div class="combined-content">
                  <!-- 左侧：干支（竖向） -->
                  <div class="ganzhi-part">
                    <div class="gan">
                      {{ item.ganZhi ? item.ganZhi[0] : "" }}
                    </div>
                    <div class="zhi">
                      {{ item.ganZhi ? item.ganZhi[1] : "" }}
                    </div>
                  </div>
                  <!-- 右侧：十神（竖向） -->
                  <div class="shishen-part">
                    <div class="shishen1">
                      {{ item.shiShen1 ? item.shiShen1[0] : "" }}
                    </div>
                    <div class="shishen2">
                      {{ item.shiShen2 ? item.shiShen2[0] : "" }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, watchEffect, ref, watch } from "vue";
import api from "@/api/index";
import {
  getTianGan,
  getDiZhi,
  getZhuXing,
  getCangGan,
  getDiShi,
  getDaYunData,
  getLiuNianData
} from "@/utils/baziDataUtils";

interface Props {
  baziResult: any;
  liupanResult: any;
  profileData: any;
  onDaYunUpdate?: (newLiupanData: any) => void;
  onLiuNianUpdate?: (newLiupanData: any) => void;
  onLiuYueUpdate?: (newLiupanData: any) => void;
}

const props = defineProps<Props>();

// 选择状态
const selectedDaYunIndex = ref(0); // 大运默认选中第一个（第1轮）
const selectedLiuNianIndex = ref(0); // 流年默认选中第一个（第1轮）
const selectedLiuYueIndex = ref(-1); // 流月默认不选中（0轮表示未选中）

// 计算轮数的辅助函数（如果需要的话可以使用）
// const getDaYunLun = () => selectedDaYunIndex.value + 1; // 大运轮数：索引+1
// const getLiuNianLun = () => selectedLiuNianIndex.value + 1; // 流年轮数：索引+1
// const getLiuYueLun = () =>
//   selectedLiuYueIndex.value === -1 ? 0 : selectedLiuYueIndex.value + 1; // 流月轮数：未选中=0，选中=索引+1

// 选择方法
const selectDaYun = async (index: number) => {
  selectedDaYunIndex.value = index;
  // 选择大运时，重置流年为第1个，流月为未选中
  selectedLiuNianIndex.value = 0;
  selectedLiuYueIndex.value = -1;

  // 调用大运更新接口
  await updateDaYunLiuNian(index);
};

const selectLiuNian = async (index: number) => {
  selectedLiuNianIndex.value = index;
  // 选择流年时，重置流月为未选中
  selectedLiuYueIndex.value = -1;

  // 调用流年更新接口
  await updateLiuNian(index);
};

// 大运更新接口调用
const updateDaYunLiuNian = async (daYunIndex: number) => {
  if (!props.baziResult || !props.profileData || !daYunList.value[daYunIndex]) {
    return;
  }

  const selectedDaYun = daYunList.value[daYunIndex];

  // 构建接口参数
  const updateParams = {
    yearGan: props.baziResult.baZi?.[0]?.charAt(0) || "",
    monthGan: props.baziResult.baZi?.[1]?.charAt(0) || "",
    dayGan: props.baziResult.baZi?.[2]?.charAt(0) || "",
    hourGan: props.baziResult.baZi?.[3]?.charAt(0) || "",
    daYunGan: selectedDaYun.ganZhi?.charAt(0) || "", // 大运天干
    yearZhi: props.baziResult.baZi?.[0]?.charAt(1) || "",
    monthZhi: props.baziResult.baZi?.[1]?.charAt(1) || "",
    dayZhi: props.baziResult.baZi?.[2]?.charAt(1) || "",
    hourZhi: props.baziResult.baZi?.[3]?.charAt(1) || "",
    daYunZhi: selectedDaYun.ganZhi?.charAt(1) || "", // 大运地支
    daYunLun: daYunIndex + 1, // 大运轮数（从1开始）
    liuNianLun: selectedLiuNianIndex.value + 1, // 流年轮数（当前选中的流年轮数）
    date: props.profileData.realSolarTime || "",
    season: props.baziResult.season || "",
    yearGanZhiNaYinWuXing: props.baziResult.yearGanZhiNaYin?.slice(-1) || "",
    sex: props.profileData.gender || 0,
    qiYunLiuPai: 1
  };

  try {
    const response: any = await api.paipan.daYunUpdate(updateParams);

    // 接口直接返回数据，不是 {code, data} 格式
    if (response && typeof response === "object") {
      // 通知父组件更新数据
      if (props.onDaYunUpdate) {
        props.onDaYunUpdate(response);
      }
    }
  } catch (error) {
    // 处理错误
  }
};

// 流年更新接口调用
const updateLiuNian = async (liuNianIndex: number) => {
  if (
    !props.baziResult ||
    !props.profileData ||
    !liuNianList.value[liuNianIndex] ||
    !daYunList.value[selectedDaYunIndex.value]
  ) {
    return;
  }

  const selectedDaYun = daYunList.value[selectedDaYunIndex.value];
  const selectedLiuNian = liuNianList.value[liuNianIndex];

  // 构建接口参数
  const updateParams = {
    yearGan: props.baziResult.baZi?.[0]?.charAt(0) || "",
    monthGan: props.baziResult.baZi?.[1]?.charAt(0) || "",
    dayGan: props.baziResult.baZi?.[2]?.charAt(0) || "",
    hourGan: props.baziResult.baZi?.[3]?.charAt(0) || "",
    daYunGan: selectedDaYun.ganZhi?.charAt(0) || "", // 大运天干
    liuNianGan: selectedLiuNian.ganZhi?.charAt(0) || "", // 流年天干
    yearZhi: props.baziResult.baZi?.[0]?.charAt(1) || "",
    monthZhi: props.baziResult.baZi?.[1]?.charAt(1) || "",
    dayZhi: props.baziResult.baZi?.[2]?.charAt(1) || "",
    hourZhi: props.baziResult.baZi?.[3]?.charAt(1) || "",
    daYunZhi: selectedDaYun.ganZhi?.charAt(1) || "", // 大运地支
    liuNianZhi: selectedLiuNian.ganZhi?.charAt(1) || "", // 流年地支
    daYunLun: selectedDaYunIndex.value + 1, // 大运轮数（当前选中的大运轮数）
    liuNianLun: liuNianIndex + 1, // 流年轮数（从1开始）
    date: props.profileData.realSolarTime || "",
    season: props.baziResult.season || "",
    yearGanZhiNaYinWuXing: props.baziResult.yearGanZhiNaYin?.slice(-1) || "",
    sex: props.profileData.gender || 0,
    qiYunLiuPai: 1
  };

  try {
    const response: any = await api.paipan.liuNianUpdate(updateParams);

    if (response && typeof response === "object") {
      // 通知父组件更新数据
      if (props.onLiuNianUpdate) {
        props.onLiuNianUpdate(response);
      }
    }
  } catch (error) {
    // 处理错误
  }
};

// 流月选择函数
const selectLiuYue = async (index: number) => {
  selectedLiuYueIndex.value = index;

  // 调用流月更新接口
  await updateLiuYue(index);
};

// 获取行背景色类名（深浅深模式）- 暂时不使用
// const getRowClass = (rowIndex: number) => {
//   const pattern = rowIndex % 3;
//   switch (pattern) {
//     case 0: return 'row-dark';   // 深色
//     case 1: return 'row-light';  // 浅色
//     case 2: return 'row-dark';   // 深色
//     default: return 'row-light';
//   }
// };

// 获取年龄显示文本
const getAgeDisplay = (item: any, index: number) => {
  if (index === 0 && daYunList.value.length > 1) {
    // 第一项显示年龄范围
    const startAge = parseInt(item.age);
    const endAge = parseInt(daYunList.value[1].age) - 1;
    return `${startAge}~${endAge}岁`;
  } else {
    // 其他项显示单个年龄
    return `${item.age}岁`;
  }
};

// 流月更新接口调用
const updateLiuYue = async (liuYueIndex: number) => {
  if (
    !props.baziResult ||
    !props.profileData ||
    !liuYueList.value[liuYueIndex] ||
    !daYunList.value[selectedDaYunIndex.value] ||
    !liuNianList.value[selectedLiuNianIndex.value]
  ) {
    return;
  }

  const selectedDaYun = daYunList.value[selectedDaYunIndex.value];
  const selectedLiuNian = liuNianList.value[selectedLiuNianIndex.value];
  const selectedLiuYue = liuYueList.value[liuYueIndex];

  // 从真太阳时中提取年月信息
  const solarDate = new Date(props.profileData.realSolarTime);
  const solarYear = solarDate.getFullYear();
  const solarMonth = solarDate.getMonth() + 1; // 月份从0开始，需要+1

  // 构建接口参数
  const updateParams = {
    yearGan: props.baziResult.baZi?.[0]?.charAt(0) || "",
    monthGan: props.baziResult.baZi?.[1]?.charAt(0) || "",
    dayGan: props.baziResult.baZi?.[2]?.charAt(0) || "",
    hourGan: props.baziResult.baZi?.[3]?.charAt(0) || "",
    daYunGan: selectedDaYun.ganZhi?.charAt(0) || "", // 大运天干
    liuNianGan: selectedLiuNian.ganZhi?.charAt(0) || "", // 流年天干
    liuYueGan: selectedLiuYue.ganZhi?.charAt(0) || "", // 流月天干
    yearZhi: props.baziResult.baZi?.[0]?.charAt(1) || "",
    monthZhi: props.baziResult.baZi?.[1]?.charAt(1) || "",
    dayZhi: props.baziResult.baZi?.[2]?.charAt(1) || "",
    hourZhi: props.baziResult.baZi?.[3]?.charAt(1) || "",
    daYunZhi: selectedDaYun.ganZhi?.charAt(1) || "", // 大运地支
    liuNianZhi: selectedLiuNian.ganZhi?.charAt(1) || "", // 流年地支
    liuYueZhi: selectedLiuYue.ganZhi?.charAt(1) || "", // 流月地支
    daYunLun: selectedDaYunIndex.value + 1, // 大运轮数（当前选中的大运轮数）
    liuNianLun: selectedLiuNianIndex.value + 1, // 流年轮数（当前选中的流年轮数）
    liuYueLun: liuYueIndex + 1, // 流月轮数（从1开始，用户主动选择）
    solarYear: solarYear, // 公历年
    solarMonth: solarMonth, // 公历月
    date: props.profileData.realSolarTime || "",
    season: props.baziResult.season || "",
    yearGanZhiNaYinWuXing: props.baziResult.yearGanZhiNaYin?.slice(-1) || "",
    sex: props.profileData.gender || 0,
    qiYunLiuPai: 1
  };

  try {
    const response: any = await api.paipan.liuYueUpdate(updateParams);

    if (response && typeof response === "object") {
      // 通知父组件更新数据
      if (props.onLiuYueUpdate) {
        props.onLiuYueUpdate(response);
      }
    }
  } catch (error) {
    // 处理错误
  }
};

// 计算大运、流年、流月数据
const daYunData = computed(() => {
  const result = getDaYunData(props.liupanResult);
  return result;
});
const liuNianData = computed(() => getLiuNianData(props.liupanResult));
// 流月数据：根据选择状态动态计算
const liuYueData = computed(() => {
  // 如果没有选择流月，返回空数据
  if (selectedLiuYueIndex.value === -1 || !props.liupanResult) {
    return { tianGan: "", diZhi: "", cangGan: [], diShi: "", zhuXing: "" };
  }

  // 获取当前选中的流月项
  const selectedLiuYue = liuYueList.value[selectedLiuYueIndex.value];

  // 优先从接口返回的数据中获取，如果没有则从选中的流月项中获取干支
  const ganZhi = selectedLiuYue?.ganZhi || "";
  const result = {
    tianGan:
      props.liupanResult.liuYueGan ||
      props.liupanResult.liuYueGanZhi?.charAt(0) ||
      ganZhi.charAt(0) ||
      "",
    diZhi:
      props.liupanResult.liuYueZhi ||
      props.liupanResult.liuYueGanZhi?.charAt(1) ||
      ganZhi.charAt(1) ||
      "",
    cangGan: props.liupanResult.liuYueCangGan || [],
    diShi: props.liupanResult.liuYueDiShi || "",
    zhuXing: props.liupanResult.liuYueZhuXing || ""
  };

  return result;
});

// 处理大运列表数据
const daYunList = computed(() => {
  if (!props.liupanResult?.daYun || !Array.isArray(props.liupanResult.daYun)) {
    return [];
  }

  return props.liupanResult.daYun.map((item: any[]) => ({
    year: item[0] || "", // 年份
    age: item[1] || "", // 年龄
    ganZhi: item[2] || "", // 干支
    shiShen1: item[3] || "", // 十神1
    shiShen2: item[4] || "" // 十神2
  }));
});

// 处理流年列表数据
const liuNianList = computed(() => {
  if (
    !props.liupanResult?.liuNian ||
    !Array.isArray(props.liupanResult.liuNian)
  ) {
    return [];
  }

  return props.liupanResult.liuNian.map((item: any[]) => ({
    year: item[0] || "", // 年份
    age: item[1] || "", // 年龄
    ganZhi: item[2] || "", // 干支
    shiShen1: item[3] || "", // 十神1
    shiShen2: item[4] || "" // 十神2
  }));
});

// 处理流月列表数据
const liuYueList = computed(() => {
  if (
    !props.liupanResult?.liuYue ||
    !Array.isArray(props.liupanResult.liuYue)
  ) {
    return [];
  }

  return props.liupanResult.liuYue.map((item: any[]) => ({
    monthNum: item[0] || "", // 月份数字（不展示，仅用于内部逻辑）
    monthName: item[1] || "", // 节气名称
    monthDisplay: item[2] || "", // 节气日期
    ganZhi: item[3] || "", // 流月干支
    shiShen1: item[4] || "", // 十神1
    shiShen2: item[5] || "" // 十神2
  }));
});

// 监听数据变化并确保选择状态有效
watchEffect(() => {
  if (props.baziResult && props.liupanResult) {
    // 确保选择状态在有效范围内
    if (
      daYunList.value.length > 0 &&
      selectedDaYunIndex.value >= daYunList.value.length
    ) {
      selectedDaYunIndex.value = 0;
      selectedLiuNianIndex.value = 0; // 重置流年
      selectedLiuYueIndex.value = -1; // 重置流月
    }
    if (
      liuNianList.value.length > 0 &&
      selectedLiuNianIndex.value >= liuNianList.value.length
    ) {
      selectedLiuNianIndex.value = 0;
      selectedLiuYueIndex.value = -1; // 重置流月
    }

    // 当流月数据变化时，重置流月选择状态
    if (
      liuYueList.value.length > 0 &&
      selectedLiuYueIndex.value >= liuYueList.value.length
    ) {
      selectedLiuYueIndex.value = -1; // 流月默认不选中
    }
  }
});

// 监听 props.liupanResult 的变化
watch(
  () => props.liupanResult,
  () => {
    // 数据变化时的处理逻辑
  },
  { deep: true }
);
</script>

<style scoped lang="less">
.liupan-result-display {
  padding: 0;
  margin: 0;
}

/* 流盘表格容器 - 直接顶到边缘 */
.liupan-tables-container {
  margin: 0;
  padding: 0;
}

/* 流盘表格样式 */
.liupan-table-container {
  padding: 0;
  margin: 0;
  overflow-x: auto;
}

.liupan-table {
  width: 100%;
  border-collapse: collapse;
  display: table;
  min-width: 600px;
  margin: 0;
}

.table-header,
.table-row {
  display: table-row;
}

.header-cell,
.row-cell {
  display: table-cell;
  padding: 8px 4px;
  text-align: center;
  vertical-align: middle;
  border: none;
  font-size: 14px;
}

.category-header,
.category-cell {
  font-weight: 600;
  color: #374151;
  min-width: 60px;
}

.header-cell {
  font-weight: 600;
  color: #374151;
}

// 第一张表的行颜色逻辑：深-深-浅-浅-深-浅
.liupan-table {
  // 第1行 标题行：深色 (nth-child(1))
  .table-header {
    background-color: #f2efe9;

    .header-cell {
      background-color: #f2efe9;
    }
  }

  // 第2行 主星行：深色 (nth-child(2))
  .table-row:nth-child(2) {
    background-color: #f2efe9;

    .row-cell {
      background-color: #f2efe9;
    }
  }

  // 第3行 天干行：浅色 (nth-child(3))
  .table-row:nth-child(3) {
    background-color: #f6f5f1;

    .row-cell {
      background-color: #f6f5f1;
    }
  }

  // 第4行 地支行：浅色 (nth-child(4))
  .table-row:nth-child(4) {
    background-color: #f6f5f1;

    .row-cell {
      background-color: #f6f5f1;
    }
  }

  // 第5行 藏干行：深色 (nth-child(5))
  .table-row:nth-child(5) {
    background-color: #f2efe9;

    .row-cell {
      background-color: #f2efe9;
    }
  }

  // 第6行 地势行：浅色 (nth-child(6))
  .table-row:nth-child(6) {
    background-color: #f6f5f1;

    .row-cell {
      background-color: #f6f5f1;
    }
  }
}

/* 天干地支颜色 */
.tiangan-cell {
  color: #3b82f6;
  font-weight: 600;
  font-size: 18px;
}

.dizhi-cell {
  color: #10b981;
  font-weight: 600;
  font-size: 18px;
}

/* 藏干样式 */
.canggan-content {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.canggan-item {
  font-size: 12px;
  color: #6b7280;
}

.empty-placeholder {
  color: #9ca3af;
}

/* 大运流年流月详情样式 */
.liupan-detail-card {
  background-color: transparent;
  border: none;
  border-radius: 0;
  margin: 0;
  padding: 0;
  box-shadow: none;
  backdrop-filter: none;
}

.card-header {
  display: none;
}

.dayun-section,
.liunian-section,
.liuyue-section {
  margin: 0;
  border-radius: 0;
  overflow: hidden;
}

.section-title {
  background-color: #f2efe9;
  padding: 8px 12px;
  font-weight: 600;
  font-size: 14px;
  color: #374151;
}

/* 大运样式 */
.dayun-section {
  background-color: #f2efe9;
  border-radius: 0;
  overflow: hidden;
}

.dayun-section .section-title {
  background-color: #f2efe9;
  font-weight: 600;
  padding: 8px 12px;
  color: inherit;
}

.dayun-table {
  display: flex;
  flex-direction: column;
  background-color: #f2efe9;
}

.dayun-row {
  display: flex;
}

.dayun-cell {
  flex: 1;
  padding: 8px 4px;
  text-align: center;
  cursor: pointer;
  min-height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  min-width: 60px;
  background-color: #f2efe9;
}

/* 移除悬停效果 */

/* 选中状态：只给八字部分添加椭圆边框（使用box-shadow避免布局偏移） */
.dayun-cell.selected .ganzhi-part {
  box-shadow: 0 0 0 2px #d69e2e;
  border-radius: 50%;
  padding: 4px 6px;
}

/* 干支+十神组合布局 */
.combined-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.ganzhi-part,
.shishen-part {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
}

.gan,
.zhi {
  font-weight: 600;
  font-size: 14px;
  line-height: 1.2;
}

.shishen1,
.shishen2 {
  font-size: 12px;
  line-height: 1.2;
}

/* 十神样式 */
.shishen-cell {
  font-size: 12px;
}

/* 年龄和年份行样式 */
.age-cell {
  font-weight: 600;
  font-size: 12px;
}

.year-cell {
  font-size: 12px;
}

/* 移除年份行选中背景色 */

.dayun-content {
  background-color: #f6f5f1;
}

.ganzhi-row,
.shishen-row {
  display: flex;
  border-bottom: 1px solid #e5e7eb;
}

.ganzhi-cell,
.shishen-cell {
  flex: 1;
  padding: 6px 4px;
  text-align: center;
  font-size: 12px;
  border-right: 1px solid #e5e7eb;
  min-width: 60px;
  cursor: pointer;
  transition: background-color 0.2s;
  background-color: #f6f5f1;
}

.ganzhi-cell:hover,
.shishen-cell:hover {
  background-color: #f3f4f6;
}

.ganzhi-cell {
  font-weight: 600;
}

/* 移除干支和十神选中背景色 */

/* 流年样式 - 使用浅米色背景 */
.liunian-section {
  background-color: #f6f5f1;
  border-radius: 0;
  overflow: hidden;
}

.liunian-section .section-title {
  background-color: #f6f5f1;
  font-weight: 600;
  padding: 8px 12px;
  color: inherit;
}

.liunian-table {
  display: flex;
  flex-direction: column;
  background-color: #f6f5f1;
}

.liunian-row {
  display: flex;
}

.liunian-cell {
  flex: 1;
  padding: 8px 4px;
  text-align: center;
  cursor: pointer;
  min-height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  min-width: 60px;
  background-color: #f6f5f1;
}

/* 选中状态：只给八字部分添加椭圆边框 */
.liunian-cell.selected .ganzhi-part {
  box-shadow: 0 0 0 2px #d69e2e;
  border-radius: 50%;
  padding: 4px 6px;
}

/* 流月样式 - 复用大运样式 */
.liuyue-section {
  background-color: #f2efe9;
  border-radius: 0;
  overflow: hidden;
}

.liuyue-section .section-title {
  background-color: #f2efe9;
  font-weight: 600;
  padding: 8px 12px;
  color: inherit;
}

.liuyue-table {
  display: flex;
  flex-direction: column;
  background-color: #f2efe9;
}

.liuyue-row {
  display: flex;
}

.liuyue-cell {
  flex: 1;
  padding: 8px 4px;
  text-align: center;
  cursor: pointer;
  min-height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  min-width: 40px;
  background-color: #f2efe9;
}

/* 选中状态：只给八字部分添加椭圆边框 */
.liuyue-cell.selected .ganzhi-part {
  box-shadow: 0 0 0 2px #d69e2e;
  border-radius: 50%;
  padding: 4px 6px;
}
</style>
