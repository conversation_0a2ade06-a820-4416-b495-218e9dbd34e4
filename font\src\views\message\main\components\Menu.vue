<template>
  <div class="message-menu flex justify-between gap-[117px]">
    <div @click="toSystemMessage">
      <svg-icon name="bx-bell" class-name="system-icon" />
      <span class="system-font pt-3">{{ $t("message.top") }}</span>
    </div>
    <div @click="toActivityMessage">
      <svg-icon name="bx-calendar-heart" class-name="event-icon" />
      <span class="event-font pt-3">活动公告</span>
    </div>
  </div>
</template>
<script setup lang="ts">
const router = useRouter();

const toSystemMessage = () => {
  router.push("/message/system");
};

const toActivityMessage = () => {
  router.push("/message/activity");
};
</script>

<style scoped>
.message-menu {
  width: 100%;
  min-height: 116px;
  border-radius: 16px;

  box-shadow: 0 4px 10px 0 rgba(0, 0, 0, 0.1);
}

.system-icon {
  margin-top: 24px;
  margin-left: 75px;
  width: 34px;
  height: 34px;
}

.system-font {
  margin-left: 59px;
  width: 67px;
  height: 28px;

  font-family: Microsoft YaHei;
  font-size: 14px;
  font-weight: normal;
  line-height: 18px;
  display: flex;
  align-items: center;
  letter-spacing: 0.15em;

  font-variation-settings: "opsz" auto;
  color: #666666;
}

.event-icon {
  margin-top: 24px;
  margin-left: 17px;

  width: 34px;
  height: 34px;
}

.event-font {
  margin-right: 65px;
  width: 67px;
  height: 28px;

  font-family: Microsoft YaHei;
  font-size: 14px;
  font-weight: normal;
  line-height: 18px;
  display: flex;
  align-items: center;
  letter-spacing: 0.15em;

  font-variation-settings: "opsz" auto;
  color: #666666;
}
</style>
