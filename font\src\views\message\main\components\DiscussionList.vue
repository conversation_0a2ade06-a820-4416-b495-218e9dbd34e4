<template>
  <div>
    <van-list
      v-model:loading="loading"
      v-model:error="error"
      :finished="finished"
      :finished-text="$t('system.noMore')"
      error-text="请求失败，点击重新加载"
    >
      <van-swipe-cell v-for="item in data" :key="item.id">
        <van-cell>
          <template #title>
            <discussion-item
              :row="item"
              @click="openDetail(item.discussionId)"
            />
          </template>
        </van-cell>
        <template #right>
          <!-- todo 删除 -->
          <van-button
            square
            type="danger"
            :text="$t('system.delete')"
            style="height: 100%"
          />
        </template>
      </van-swipe-cell>
    </van-list>
  </div>
</template>
<script setup lang="ts">
import discussionItem from "@/views/message/main/components/DisscussionListItem.vue";
import { useData } from "@/hooks/useData";
import api from "@/api/index";
const props = defineProps({
  active: {
    type: Number,
    default: 2
  }
});

const finished = ref(false);
const { error, data, loading, execute, onFetchResponse } = useData(
  api.discussion.myDiscussion
);

onFetchResponse(() => {
  finished.value = true;
});

watch(
  () => props.active,
  newVal => {
    finished.value = false;
    execute({ status: newVal });
  },
  { immediate: true }
);

// 删除

const router = useRouter();
const openDetail = (id: string) => {
  router.push({ path: "/message/detail", query: { id } });
};
</script>

<style scoped></style>
