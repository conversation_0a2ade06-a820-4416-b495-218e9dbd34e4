<template>
  <div>
    <div class="mt-4 flex justify-between">
      <p class="title">{{ row.postTitle }}</p>
      <p class="time">{{ formatDisplayDate(row.publishTime) }}</p>
    </div>
    <p class="mt-[8px] content">
      {{ row.postContent }}
    </p>
  </div>
</template>
<script setup lang="ts">
import { Discussion } from "@/views/message/message";
import { formatDisplayDate } from "@/utils/DateUtils";

// 定义 props
const props = defineProps<{ row: Discussion }>();
</script>

<style scoped>
.title {
  font-family: Roboto;
  font-size: 16px;
  font-weight: 500;
  line-height: 24px;
  letter-spacing: 0px;

  font-feature-settings: "kern" on;
  color: #1f2937;
}

.content {
  min-height: 42px;
  font-family: Roboto;
  font-size: 14px;
  font-weight: normal;
  line-height: 21px;
  letter-spacing: 0px;
  font-feature-settings: "kern" on;
  color: #4b5563;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.time {
  font-family: Roboto;
  font-size: 12px;
  font-weight: normal;
  line-height: 18px;
  letter-spacing: 0px;

  font-feature-settings: "kern" on;
  color: #9ca3af;
}
</style>
