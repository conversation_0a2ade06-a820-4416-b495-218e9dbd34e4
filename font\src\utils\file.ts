export const judgeFileType = (str: string) => {
  const videoRegex =
    /(\.wmv|\.mp4|\.flv|\.avi|\.rmvb|\.mpg|\.mkv|\.mov|\.mts)$/i;
  const audioRegex = /(\.w4v|\.m4a|\.wma|\.wav|\.mp3|\.amr)$/i;
  const imgRegex = /(\.jpg|\.jpeg|\.gif|\.png|\.bmp)$/i;
  const fileRegex = /(\.doc|\.docx|\.ppt|\.pptx|\.xls|\.xlsx|\.pps|\.pdf)$/i;
  if (fileRegex.test(str)) {
    return "FILE";
  }
  if (imgRegex.test(str)) {
    return "IMG";
  }
  if (videoRegex.test(str)) {
    return "VIDEO";
  }
  if (audioRegex.test(str)) {
    return "AUDIO";
  }
  return "FILE";
};
