import { createApp } from "vue";
import { createPinia } from "pinia";
import piniaPluginPersistedstate from "pinia-plugin-persistedstate";
import "./styles/style.css";
import "./assets/tailwind.css";
import router from "./router";
import App from "./App.vue";
// i18n
import { i18n, vantLocales } from "./locales";
// svg icon
import "virtual:svg-icons-register";

vantLocales(i18n.global.locale.value);
const app = createApp(App);
app.use(i18n);
const pinia = createPinia();
pinia.use(piniaPluginPersistedstate);
app.use(pinia);
app.use(router);
app.mount("#app");
