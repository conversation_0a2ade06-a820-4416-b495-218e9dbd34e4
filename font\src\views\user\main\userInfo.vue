<template>
  <div class="box">
    <div class="avatar" />
    <div class="info">
      <div class="name">{{ userInfo.username }}</div>
      <div class="flex items-center h-[20px] vip">
        <svg-icon name="vip" :class-name="isVipUser ? 'vip-active-color' : 'vip-color'" />
        <span v-if="isVipUser">
          {{ $t("user.vip.validity", { date: userInfo.vip.endTime }) }}
        </span>
        <span v-else>尚未开通</span>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { useUserStore } from "@/store/modules/user";
const store = useUserStore();

const { userInfo } = storeToRefs(store);

// 基于roles数组判断是否为VIP用户
const isVipUser = computed(() => {
  return userInfo.value.roles?.includes("VIP用户") || false;
});

console.log("userInfo", userInfo.value);
</script>
<style scoped>
.box {
  border-radius: 16px;
  opacity: 1;
  background: #ffffff;
  height: 96px;
  display: flex;
  gap: 16px;
  padding: 16px;
  align-items: center;
}

.avatar {
  width: 64px;
  height: 64px;
  border-radius: 100%;
  background: #d9d9d9;
  /* background: url("@/assets/avatar.png"); */
}

.info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.name {
  font-family: Roboto;
  font-size: 18px;
  font-weight: 500;
  line-height: 28px;
  letter-spacing: 0px;

  color: #000000;
}

.vip {
  font-family: Roboto;
  font-size: 14px;
  font-weight: normal;
  line-height: 20px;
  letter-spacing: 0px;
  color: #4b5563;
}

.vip-color {
  color: #787878;
  margin-right: 8px;
}

.vip-active-color {
  color: #fbbf24;
  margin-right: 8px;
}
</style>
