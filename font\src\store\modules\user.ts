import { defineStore } from "pinia";
import type { Info } from "@/views/user/user";

export const useUserStore = defineStore("phone-user-info", {
  state: () => ({
    userInfo: {} as Info
  }),
  actions: {
    setUserInfo(data: Info) {
      this.userInfo = data;
      // 移除强制设置VIP状态，使用后端返回的真实数据
    },
    clearUserInfo() {
      this.userInfo = {} as Info;
      localStorage.removeItem("Authorization");
    },
    // 新增：检查用户是否为VIP的getter方法
    isVip(): boolean {
      return this.userInfo.roles?.includes("VIP用户") || false;
    }
  },
  persist: true
});
