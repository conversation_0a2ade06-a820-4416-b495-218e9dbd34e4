<template>
  <div class="h-full flex flex-col gap-4">
    <setting-top />
    <div class="px-6 flex flex-col gap-4">
      <user-info />
      <user-security />
      <general-setting />
    </div>
    <button class="logout-btn" @click="logout">
      {{ $t("setting.logout") }}
    </button>
  </div>
</template>
<script setup lang="ts">
import SettingTop from "./top.vue";
import UserInfo from "@/views/setting/main/userInfo.vue";
import UserSecurity from "@/views/setting/main/userSecurity.vue";
import GeneralSetting from "@/views/setting/main/generalSetting.vue";
import { useUserStore } from "@/store/modules/user";

// 退出登录, 清空token等信息
const router = useRouter();
const userStore = useUserStore();
const logout = () => {
  userStore.clearUserInfo();
  router.push("/login");
};
</script>

<style scoped>
.logout-btn {
  position: absolute;
  left: 0;
  right: 0;
  height: 48px;
  bottom: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0 30px;
  border: none;
  background-color: #6366f1;
  color: #ffffff;
  border-radius: 4px;
}
</style>
