<template>
  <div>
    <van-list>
      <van-checkbox-group ref="checkboxGroup" v-model="checked">
        <van-cell-group>
          <van-cell
            v-for="item in draftList"
            :key="item.discussionId"
            @click="toggle(item.discussionId)"
          >
            <template #title>
              <draft-item
                :row="item"
                :checkbox-refs="checkboxRefs"
                :show-delete="showDelete"
              />
            </template>
          </van-cell>
        </van-cell-group>
      </van-checkbox-group>
    </van-list>
    <div v-if="showDelete" class="px-25 mt-4 mb-17.5 flex items-center">
      <button class="confirm-button" @click="deleteHandler">
        {{ $t("system.delete") }}
      </button>
      <van-checkbox
        v-model="allChecked"
        class="absolute right-2"
        @click="toggleAll"
      >
        <template #icon>
          <van-icon name="check" />
        </template>
        {{ $t("system.allCheck") }}
      </van-checkbox>
    </div>
  </div>
</template>
<script setup lang="ts">
import { Discussion } from "@/views/message/message";
import DraftItem from "@/views/message/draft/components/DraftListItem.vue";
import { useData } from "@/hooks/useData";
import api from "@/api/index";

const props = defineProps({
  showDelete: {
    type: Boolean,
    default: false
  }
});
const checked = ref([]);
const checkboxGroup = ref(null);
const checkboxRefs = ref({});

// 全选
const allChecked = computed(() => {
  return checked.value.length === data.value.length;
});

const toggle = id => {
  checkboxRefs.value[id].toggle();
};

const toggleAll = () => {
  if (checked.value.length === data.value.length) {
    checkboxGroup.value.toggleAll(false);
    return;
  }
  checkboxGroup.value.toggleAll(true);
};

// 删除
const {
  error,
  loading,
  execute: deleteBatchDiscussion,
  onFetchResponse
} = useData(api.discussion.deleteBatchDiscussion);

onFetchResponse(() => {
  loadDraft();
  checked.value = [];
});

const deleteHandler = async () => {
  if (checked.value.length === 0) {
    return;
  }
  await deleteBatchDiscussion({ discussionIds: checked.value });
};

// 加载
const { data, execute: executeMyDraft } = useData(api.discussion.myDraft);
const draftList = ref<Discussion[]>([]);

const loadDraft = async () => {
  await executeMyDraft({});
  draftList.value = data.value || [];
};

onMounted(() => {
  loadDraft();
});
</script>

<style scoped>
.confirm-button {
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  width: 100%;
  height: 48px;
  background-color: #7b68ee;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 16px;
  cursor: pointer;
}

:deep(.van-cell) {
  padding: 0;
}

:deep(.van-checkbox__icon--checked .van-icon) {
  background-color: #7b7bf7;
  border-color: #7b7bf7;
}
</style>
