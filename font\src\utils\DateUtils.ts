import dayjs from "dayjs";
import isToday from "dayjs/plugin/isToday";
import isYesterday from "dayjs/plugin/isYesterday";
import { i18n } from "@/locales";
import { SolarTime, LunarHour } from "tyme4ts";

// 扩展 dayjs 插件
dayjs.extend(isToday);
dayjs.extend(isYesterday);

const { t } = i18n.global;

/**
 * 格式化日期
 * @param date 日期对象或字符串
 * @param format 格式字符串，默认为 'YYYY-MM-DD HH:mm:ss'
 * @returns 格式化后的日期字符串
 */
export function formatDate(
  date: string | Date | number | undefined | null,
  format = "YYYY-MM-DD HH:mm:ss"
): string {
  if (!date) return "";
  return dayjs(date).format(format);
}

/**
 * 格式化日期时间
 * @param date 时间字符串或 Dayjs 对象
 * @returns 格式化后的字符串 (今天显示 HH:mm, 昨天显示 "昨天", 更早显示 YYYY-MM-DD)
 */
export function formatDisplayDate(
  date: string | Date | dayjs.Dayjs | undefined | null
): string {
  if (!date) {
    return ""; // 如果时间不存在，返回空字符串
  }

  const time = dayjs(date);

  // 检查日期是否有效
  if (!time.isValid()) {
    console.error("Invalid date format:", date);
    // 如果是字符串，直接返回原始字符串，否则返回空
    return typeof date === "string" ? date : "";
  }

  if (time.isToday()) {
    // 今天：显示 HH:mm
    return time.format("HH:mm");
  } else if (time.isYesterday()) {
    // 昨天：显示“昨天”
    return t("system.yesterday");
  } else {
    // 更早：显示 YYYY-MM-DD
    return time.format("YYYY-MM-DD");
  }
}

/**
 * date to solarTime
 * @param date 时间字符串 或 Date
 * @returns solarTime
 */
export function dateToSolarTime(date: string | Date): SolarTime {
  if (typeof date === "string") {
    date = new Date(date);
  }
  return SolarTime.fromYmdHms(
    date.getFullYear(),
    date.getMonth() + 1,
    date.getDate(),
    date.getHours(),
    date.getMinutes(),
    date.getSeconds()
  );
}

/**
 * date to LunarHour
 * @param date 时间字符串 或 Date
 * @returns lunarTime
 */
export function dateToLunarHour(date: string | Date): LunarHour {
  if (typeof date === "string") {
    date = new Date(date);
  }
  return LunarHour.fromYmdHms(
    date.getFullYear(),
    date.getMonth() + 1,
    date.getDate(),
    date.getHours(),
    date.getMinutes(),
    date.getSeconds()
  );
}

/**
 * solarTime to date
 * @param solarTime solarTime
 * @returns date
 */
export function solarTimeToDate(solarTime: SolarTime): Date {
  return new Date(
    solarTime.getYear(),
    solarTime.getMonth() - 1,
    solarTime.getDay(),
    solarTime.getHour(),
    solarTime.getMinute(),
    solarTime.getSecond()
  );
}
