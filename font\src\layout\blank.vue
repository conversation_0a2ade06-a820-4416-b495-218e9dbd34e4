<script setup lang="ts">
import tabbar from "@/components/Tabbar/index.vue";
import NavBar from "@/components/NavBar/index.vue";
import { useCachedViewStoreHook } from "@/store/modules/cachedView";
import { useDarkMode } from "@/hooks/useToggleDarkMode";
import { computed } from "vue";
import { useRoute } from "vue-router";

const cachedViews = computed(() => {
  return useCachedViewStoreHook().cachedViewList;
});
const route = useRoute();
const isNoNav = computed(() => {
  return route.meta?.noNav;
});
</script>

<template>
  <div class="app-wrapper" :class="{ 'has-nav': !isNoNav }">
    <van-config-provider>
      <nav-bar v-if="!isNoNav" />
      <router-view v-slot="{ Component }">
        <keep-alive :include="cachedViews">
          <component :is="Component" />
        </keep-alive>
      </router-view>
    </van-config-provider>
  </div>
</template>

<style lang="less" scoped>
@import "@/styles/mixin.less";

.app-wrapper {
  .clearfix();
  position: relative;
  height: 100%;
  width: 100%;
}

.has-nav {
  padding-top: 56px;
}
</style>
