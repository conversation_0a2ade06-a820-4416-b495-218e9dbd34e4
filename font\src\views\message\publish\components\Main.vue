<template>
  <div>
    <div class="main-box">
      <input
        v-model="formData.postTitle"
        type="text"
        class="title-input"
        :placeholder="$t('discussion.publish.title')"
      />
      <textarea
        v-model="formData.postContent"
        class="content-input"
        :placeholder="$t('discussion.publish.body')"
      />
    </div>
    <div class="upload-box">
      <div class="flex flex-row flex-wrap gap-2">
        <van-uploader
          :after-read="afterRead"
          :max-size="500 * 1024"
          accept="image/*"
          :disabled="uploaderDisabled"
        >
          <div class="upload-button">+</div>
        </van-uploader>
        <div class="image-list-container">
          <div v-for="(item, index) in urls" :key="index" class="image-item">
            <img class="image-box" :src="item" />
            <button class="delete-button" @click="removeImage(index)">×</button>
          </div>
        </div>
      </div>
      <p class="upload-placeholder mt-4">
        {{ $t("discussion.publish.maxImage") }}
      </p>
    </div>
    <button class="submit-button" @click="publish">
      {{ $t("discussion.publish.submit") }}
    </button>
    <button class="draft-button" @click="toDraft">
      {{ $t("discussion.draft.top") }}
    </button>
    <van-dialog
      v-model:show="showDialog"
      :title="$t('system.tip')"
      :message="$t('discussion.publish.saveDraft')"
      show-cancel-button
      :cancel-button-text="$t('system.cancel')"
      :confirm-button-text="$t('system.confirm')"
      @confirm="saveDraft"
      @cancel="cancelDraft"
    />
  </div>
</template>

<script setup lang="ts">
import { useData } from "@/hooks/useData";
import api from "@/api/index";

const props = defineProps({
  goback: {
    type: Boolean,
    default: false
  }
});

const formData = reactive({
  postTitle: "",
  postContent: "",
  postImgUrls: ""
});

const uploaderDisabled = ref(false);
const urls = ref<string[]>([
  "https://fastly.jsdelivr.net/npm/@vant/assets/cat.jpeg",
  "https://fastly.jsdelivr.net/npm/@vant/assets/cat.jpeg",
  "https://fastly.jsdelivr.net/npm/@vant/assets/cat.jpeg",
  "https://fastly.jsdelivr.net/npm/@vant/assets/cat.jpeg",
  "https://fastly.jsdelivr.net/npm/@vant/assets/cat.jpeg"
]);
const afterRead = file => {
  if (urls.value.length < 9) {
    // 限制最多9张图片
    urls.value.push(file.content); // 使用 file.content 获取 base64 或 blob url
    console.log(file);
  } else {
    // 可以添加提示，例如使用 Vant 的 Toast
    console.warn("最多只能上传9张图片");
  }
  uploaderDisabled.value = urls.value.length >= 9;
};

const removeImage = (index: number) => {
  urls.value.splice(index, 1);
  uploaderDisabled.value = urls.value.length >= 9;
};

const router = useRouter();
const toDraft = () => {
  router.push("/message/draft");
};

// 发布帖子
const { execute: executePublish, onFetchResponse: onFetchResponsePublish } =
  useData(api.discussion.publishDiscussion);

onFetchResponsePublish(() => {
  // 发布成功后，清空表单数据
  formData.postTitle = "";
  formData.postContent = "";
  urls.value = []; // 清空图片列表
  uploaderDisabled.value = false; // 重置上传按钮状态

  router.push("/message/index");
});

const publish = async () => {
  await executePublish({
    ...formData,
    postImgUrls: urls.value.join(",") // 将图片 URL 数组转换为逗号分隔的字符串
  });
};

// 保存草稿
const showDialog = ref(false);
watch(
  () => props.goback,
  newVal => {
    // 检查标题或内容是否不为空，只有在有内容时才提示保存
    if (
      newVal &&
      (formData.postTitle.trim() !== "" ||
        formData.postContent.trim() !== "" ||
        urls.value.length > 0)
    ) {
      showDialog.value = true;
    }
  },
  { immediate: true }
);

// 保存帖子至草稿
const { execute: executeCreate, onFetchResponse: onFetchResponseCreate } =
  useData(api.discussion.createDiscussion);

const saveDraft = async () => {
  await executeCreate({
    ...formData,
    postImgUrls: urls.value.join(",") // 将图片 URL 数组转换为逗号分隔的字符串
  });
  showDialog.value = false;
  cancelDraft();
};

const cancelDraft = () => {
  router.push("/message/index");
};
</script>

<style lang="less" scoped>
.main-box {
  width: 346px;
  height: 269px;
  border-radius: 12px;
  opacity: 1;

  /* 自动布局 */
  display: flex;
  flex-direction: column;
  margin: 12px auto;
  padding: 16px;
  gap: 0px 10px;
  flex-wrap: wrap;
  align-content: flex-start;

  background:
    linear-gradient(0deg, rgba(0, 0, 0, 0.001), rgba(0, 0, 0, 0.001)), #ffffff;

  box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.05);
}

.title-input,
.content-input {
  padding: 8px 12px;
  border-radius: 4px;
  font-family: Roboto, sans-serif;
  width: 100%;
  box-sizing: border-box;
}

.title-input {
  font-size: 18px;
  font-weight: normal;
  line-height: 16px;
  color: #333; /* 输入文字颜色 */

  border-bottom: 1px solid #f3f4f6; /* 下边框颜色 */
}

.title-input::placeholder,
.content-input::placeholder {
  color: #9e9e9e; /* 占位符颜色 */
}

.content-input {
  font-size: 16px;
  font-weight: normal;
  line-height: 1.5;
  color: #333; /* 输入文字颜色 */
  min-height: 164px; /* 设置最小高度 */
  resize: vertical; /* 允许垂直调整大小 */
}

.upload-box {
  width: 346px;
  min-height: 144px;
  border-radius: 12px;
  margin: 12px auto;
  padding: 16px;

  background:
    linear-gradient(0deg, rgba(0, 0, 0, 0.001), rgba(0, 0, 0, 0.001)), #ffffff;

  box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.05);
}

.upload-button {
  width: 80px;
  height: 80px;
  border-radius: 12px;
  opacity: 1;

  font-size: 24px;
  color: #9ca3af;
  font-weight: bold;

  /* 自动布局 */
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 0px;
  gap: 0px 0px;
  flex-wrap: wrap;
  align-content: center;

  background: rgba(0, 0, 0, 0);

  box-sizing: border-box;
  border: 1px dashed #d1d5db;
}

.upload-placeholder {
  font-size: 14px;
  color: #9e9e9e;
}

/* 新增：图片列表滚动容器 */
.image-list-container {
  flex: 1; /* 让图片列表容器占据剩余空间 */
  display: flex; /* 使用 flex 布局使图片水平排列 */
  overflow-x: auto; /* 内容超出时显示水平滚动条 */
  white-space: nowrap; /* 防止图片换行 */
  padding-bottom: 5px; /* 为滚动条留出一些空间 */
  gap: 8px; /* 图片之间的间距 */
  height: 80px; /* 限制容器高度与图片一致 */
}

.image-item {
  flex-shrink: 0; /* 防止图片在 flex 布局中被压缩 */
  position: relative; /* 为绝对定位的删除按钮提供参照 */
}

.image-box {
  width: 80px;
  height: 80px;
  border-radius: 12px;
  display: block; /* 确保图片表现一致 */
}

/* 新增：删除按钮样式 */
.delete-button {
  position: absolute;
  top: 0;
  right: 0;
  width: 20px;
  height: 20px;
  background-color: rgba(0, 0, 0, 0.5); /* 半透明黑色背景 */
  color: white; /* 白色 'x' */
  border: none;
  border-radius: 0 12px 0 4px; /* 圆角与图片右上角对齐，左下角稍微圆滑 */
  font-size: 14px;
  line-height: 20px; /* 使 'x' 垂直居中 */
  text-align: center;
  cursor: pointer;
  padding: 0; /* 移除默认内边距 */
  display: flex; /* 使用 flex 居中 */
  align-items: center;
  justify-content: center;
}

.delete-button:hover {
  background-color: rgba(0, 0, 0, 0.7); /* 悬停时加深背景 */
}

/* 可选：隐藏滚动条样式 */
.image-list-container::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}
.image-list-container {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}

.submit-button {
  position: absolute;
  left: 101px;
  bottom: 64px;
  width: 173px;
  height: 54px;
  border-radius: 4px;
  opacity: 1;

  /* 自动布局 */
  display: flex;
  flex-direction: column;
  padding: 12px 0px;
  gap: 0px 10px;
  flex-wrap: wrap;
  align-content: center;

  background: #7b7bf7;

  font-family: Roboto;
  font-size: 16px;
  font-weight: 500;
  line-height: 24px;
  text-align: center;
  letter-spacing: 0px;

  font-feature-settings: "kern" on;
  color: #ffffff;
}

.draft-button {
  position: absolute;
  left: 159px;

  bottom: 33px;
  width: 57px;
  height: 22px;
  opacity: 1;

  font-family: Roboto;
  font-size: 12px;
  font-weight: normal;
  line-height: 24px;
  text-align: center;
  letter-spacing: 0px;
  text-decoration: underline;

  font-variation-settings: "opsz" auto;
  font-feature-settings: "kern" on;
  color: #9e9e9e;
}
</style>
