<template>
  <div class="box">
    <div class="avatar" />
    <div class="info">
      <div class="name">{{ userInfo.username }}</div>
    </div>
    <van-icon name="arrow" class="placeholder ml-auto" />
  </div>
</template>
<script setup lang="ts">
import { useUserStore } from "@/store/modules/user";
const store = useUserStore();

const { userInfo } = storeToRefs(store);
</script>
<style scoped>
.box {
  width: 343px;
  height: 96px;
  opacity: 1;
  border-radius: 12px;
  /* 自动布局 */
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 16px;
  gap: 16px;
  flex-wrap: wrap;
  align-content: center;

  background: rgba(0, 0, 0, 0);
  box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.05);
}

.avatar {
  width: 64px;
  height: 64px;
  border-radius: 100%;
  background: #d9d9d9;
}

.info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.name {
  font-family: Roboto;
  font-size: 18px;
  font-weight: 500;
  line-height: 28px;
  letter-spacing: 0px;

  color: #000000;
}

.vip {
  font-family: Roboto;
  font-size: 14px;
  font-weight: normal;
  line-height: 20px;
  letter-spacing: 0px;

  font-feature-settings: "kern" on;
  color: #6b7280;
}

.placeholder {
  color: #9ca3af;
}
</style>
