<template>
  <div class="nav-top">
    <van-icon
      name="arrow-left"
      class="text-white nav-back-icon"
      @click="goback"
    />
    <p class="nav-title">{{ $t("activity.top") }}</p>
  </div>
</template>

<script setup lang="ts">
const router = useRouter();

const goback = () => {
  router.back();
};
</script>

<style lang="less" scoped>
.nav-top {
  width: 100%;
  min-height: 44px;

  /* 自动布局 */
  display: flex;
  align-items: center;
  align-content: center;
  justify-content: center;

  position: sticky;
  top: 0;
  z-index: 1000;

  background: #92abff;
}

.nav-title {
  font-family: Roboto;
  font-size: 18px;
  font-weight: bold;
  line-height: 27px;
  letter-spacing: 0px;

  font-variation-settings: "opsz" auto;
  font-feature-settings: "kern" on;
  color: #ffffff;
}

.nav-back-icon {
  position: absolute;
  left: 8px;
}
</style>
