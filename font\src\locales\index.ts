// vue-i18n所需要引入的
import { createI18n } from "vue-i18n";
// 本地的文件夹
import enLocale from "./lang/en-US";
import zhLocale from "./lang/zh-CN";
// import zhhkLocale from "./zh-hk";
// vant所需要引入的
import { Locale } from "vant";
//vant中的文件夹  需要的语言和本地的语言保持一致
import enUS from "vant/lib/locale/lang/en-US";
import zhCN from "vant/lib/locale/lang/zh-CN";
// import zhHK from "vant/lib/locale/lang/zh-HK";

const messages = {
  "en-US": {
    ...enUS,
    ...enLocale
  },
  "zh-CN": {
    ...zhCN,
    ...zhLocale
  }
  // zhhk: {
  //   ...zhHK,
  //   ...zhhkLocale
  // }
};

// 更新vant组件库本身的语言变化，支持国际化
function vantLocales(lang: string) {
  if (lang === "en-US") {
    Locale.use(lang, enUS);
  } else if (lang === "zh-CN") {
    Locale.use(lang, zhCN);
  }
  // else if (lang === "zhhk") {
  //   Locale.use(lang, zhHK);
  // }
}

// 获取浏览器的语言
const browserLang = navigator.language.replace("_", "-") || "zh-CN";
const language = localStorage.getItem("language") || browserLang || "zh-CN";
localStorage.setItem("language", language);

// vue i18n
const i18n = createI18n({
  legacy: false,
  // 全局注册 $t方法
  globalInjection: true,
  //设置初始化语言
  locale: language,
  // 设置备用语言
  fallbackLocale: "zh-CN",
  messages
});

const langCode = Object.keys(messages);
// 更改语言
function changeLanguage(lang: string) {
  if (!langCode.includes(lang)) {
    lang = "zh-CN";
  }
  vantLocales(lang);
  i18n.global.locale.value = lang as "en-US" | "zh-CN";
  localStorage.setItem("language", lang);
}

export { i18n, vantLocales, changeLanguage };
