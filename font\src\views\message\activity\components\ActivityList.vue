<template>
  <div>
    <van-list
      v-model:loading="loading"
      v-model:error="error"
      :finished="finished"
      :finished-text="$t('system.noMore')"
      error-text="请求失败，点击重新加载"
      @load="onLoad"
    >
      <van-cell v-for="item in data" :key="item.id">
        <template #title>
          <activity-list-item :row="item" />
        </template>
      </van-cell>
    </van-list>
  </div>
</template>
<script setup lang="ts">
import ActivityListItem from "@/views/message/activity/components/ActivityListItem.vue";
import { useData } from "@/hooks/useData";
import api from "@/api/index";
import { Activity } from "@/views/message/message";

const finished = ref(false);
const activityList = ref<Activity[]>([]);
const { error, data, loading, execute, onFetchResponse } = useData(
  api.activity.listActivity
);

const onLoad = async () => {
  const pageNum = data.value.length / 5 + 1;
  execute({ pageNum, pageSize: 5 });
};

onFetchResponse(() => {
  finished.value = true;
  console.log("activityList", data.value);
  activityList.value = [...data.value];
});

onMounted(() => {
  onLoad();
});
</script>

<style scoped></style>
