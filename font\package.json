{"name": "fortune-telling", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --open --mode development --host 0.0.0.0", "test": "vite --open --mode test --host 0.0.0.0", "build": "vue-tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"@tailwindcss/vite": "^4.0.9", "@typescript-eslint/eslint-plugin": "^8.28.0", "@typescript-eslint/parser": "^8.28.0", "await-to-js": "^3.0.0", "axios": "^1.8.1", "dayjs": "^1.11.13", "eslint-config-prettier": "^10.1.1", "eslint-define-config": "^2.1.0", "eslint-plugin-prettier": "^5.2.5", "nprogress": "^0.2.0", "pinia": "^3.0.1", "pinia-plugin-persistedstate": "^4.2.0", "swiper": "^11.2.6", "tyme4ts": "^1.3.1", "unplugin-vue-components": "^28.4.1", "vant": "^4.9.18", "vite-plugin-svg-icons": "^2.0.1", "vue": "^3.5.13", "vue-eslint-parser": "^10.1.1", "vue-i18n": "^12.0.0-alpha.2", "vue-router": "^4.5.0"}, "devDependencies": {"@eslint/js": "^9.21.0", "@types/node": "^22.13.14", "@types/nprogress": "^0.2.3", "@vitejs/plugin-vue": "^5.2.1", "@vue/tsconfig": "^0.7.0", "eslint": "^9.21.0", "eslint-plugin-vue": "^10.0.0", "globals": "^16.0.0", "less": "^4.2.2", "typescript": "~5.7.2", "typescript-eslint": "^8.26.0", "unplugin-auto-import": "^19.1.2", "vite": "^6.2.0", "vue-tsc": "^2.2.4"}}