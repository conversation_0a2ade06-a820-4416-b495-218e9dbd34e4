<template>
  <div class="box">
    <div v-for="item in functionButtons" class="boxItem" @click="item.func">
      <div class="flex items-center">
        <svg-icon :name="item.icon" />
        <span class="px-3">{{ item.text }}</span>
      </div>
      <van-icon name="arrow" class="placeholder" />
    </div>
  </div>
</template>
<script setup lang="ts">
const { t } = useI18n();

const router = useRouter();
const functionButtons = [
  {
    text: t("user.orderHistory"),
    icon: "order-history",
    func: () => {}
  },
  {
    icon: "browsing-history",
    text: t("user.browsingHistory.top"),
    func: () => {
      router.push("/browsingHistory");
    }
  },
  {
    icon: "contact-us",
    text: t("user.contactUs.top"),
    func: () => {
      router.push("/contactUs");
    }
  }
];
</script>
<style scoped>
.box {
  border-radius: 16px;
  opacity: 1;
  background: #ffffff;
}

.boxItem {
  height: 53px;
  padding: 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #e5e7eb;
}
.boxItem:last-child {
  border-bottom: none;
}

.placeholder {
  color: #9ca3af;
}
</style>
