<template>
  <div>
    <Top />
    <ProfileForm v-model="paipanForm" />
    <button class="save-btn" @click="submit">开始排盘</button>
  </div>
</template>

<script setup lang="ts">
import ProfileForm from "@/views/profile/edit/components/ProfileForm.vue";
import Top from "@/views/paipan/main/top.vue";
import api from "@/api/index";
import type { Paipan } from "../paipan";
import { useData } from "@/hooks/useData";

const route = useRoute();

// 相当于 create时候会创建新档案, edit时候会编辑档案(edit是由已有档案跳转而来， 携带id)
// type: create | edit
const type = computed(() => route.path.split("/").pop());

const paipanForm = ref<Paipan>({
  profileId: "",
  profileName: "",
  gender: 1, // 1:男 2:女
  profileDateType: 1, // 1:公历 2:农历 3:四柱
  solarBirth: "",
  lunarBirth: "",
  fourPillars: "",
  realSolarTime: "",
  latitude: "35.0", // 纬度
  longitude: "120.0", // 经度
  isDefault: false,
  isSave: true
});

// start paipan
const {
  execute: executePaipanStarts,
  onFetchResponse: onFetchResponsePaipanStarts
} = useData(api.paipan.paipanStarts);
onFetchResponsePaipanStarts(() => {});

const submit = async () => {
  await executePaipanStarts(paipanForm.value);
};

// 加载profile
const {
  execute: executeGetProfileById,
  onFetchResponse: onFetchResponseGetProfileById
} = useData(api.profile.getProfileById);
onFetchResponseGetProfileById(data => {
  paipanForm.value = {
    ...data,
    isSave: true
  };
});

onMounted(() => {
  if (type.value === "edit") {
    executeGetProfileById({
      id: route.query.id as string
    });
  }
});
</script>

<style scoped>
.save-btn {
  background-color: #8a82f1;
  color: white;
  padding: 12px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  width: 358px;
  font-size: 16px;
  transition: background-color 0.3s;

  display: block;
  margin: 20px auto 0;
}

.save-btn:hover {
  background-color: #7065d1;
}

.consent-group {
  margin-top: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: #555;
}

.consent-group input[type="radio"] {
  margin-right: 5px;
  accent-color: #8a82f1; /* 改变单选按钮选中时的颜色 */
}
</style>
