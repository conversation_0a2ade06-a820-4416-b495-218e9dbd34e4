<template>
  <div class="h-full flex flex-col">
    <top :toggle-show-delete="toggleShowDelete" />
    <draft-list :show-delete="showDelete" />
  </div>
</template>
<script setup lang="ts">
import { ref } from "vue";
import top from "./top.vue";
import DraftList from "./components/DraftList.vue";

const showDelete = ref(false);

const toggleShowDelete = () => {
  showDelete.value = !showDelete.value;
};
</script>
<style scoped></style>
