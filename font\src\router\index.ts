import {
  createRouter,
  createWebHashHistory,
  type RouteLocationNormalized
} from "vue-router";
import routes from "./routes";
import { useCachedViewStoreHook } from "@/store/modules/cachedView";
import NProgress from "@/utils/progress";

const router = createRouter({
  history: createWebHashHistory(),
  routes
});

export interface toRouteType extends RouteLocationNormalized {
  meta: {
    title?: string;
    noCache?: boolean;
  };
}

const whiteListRouters = ["/login", "/register"];
router.beforeEach((to: toRouteType, from, next) => {
  NProgress.start();
  // 路由缓存
  useCachedViewStoreHook().addCachedView(to);
  // 开发环境
  if (import.meta.env.MODE === "development") {
    next();
    return;
  }

  // 白名单路由直接放行
  if (whiteListRouters.includes(to.path)) {
    next();
    return;
  }

  // 检查是否有 token
  const hasToken = localStorage.getItem("Authorization");
  if (!hasToken) {
    next({ path: "/login", replace: true });
    return;
  }

  next();
});

router.afterEach(() => {
  NProgress.done();
});

export default router;
