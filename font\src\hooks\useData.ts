import { ref } from "vue";
import to from "await-to-js";

// eg:
// import { useData } from "@/hooks/useData";
// import api from "@/api/index";
// const { error, data, loading, execute, onFetchResponse } = useData(
//   api.course.newest
// );
export function useData(func) {
  const loading = ref(false);
  const data = ref(null);
  const error = ref(null);
  const response = ref(null);

  // 请求
  // 请求成功
  // 请求失败
  // 请求完成
  let fetchResponseFn = null;
  const onFetchResponse = callback => {
    fetchResponseFn = callback;
  };

  let fetchErrorFn = null;
  const onFetchError = callback => {
    fetchErrorFn = callback;
  };

  let fetchFinallyFn = null;
  const onFetchFinally = callback => {
    fetchFinallyFn = callback;
  };

  const execute = async req => {
    loading.value = true;
    const [err, res] = await to(func(req));
    // console.log(err, res);
    if (!err) {
      response.value = res;
      data.value = res;
      fetchResponseFn?.(res);
    } else {
      fetchErrorFn?.(err);
    }
    loading.value = false;
    fetchFinallyFn?.(null);
    // func(req)
    //   .then(res => {
    //     response.value = res;
    //     data.value = res;
    //     console.log("!!!1", res); // {endRow: 4, firstPage: 1, hasNextPage: true, hasPreviousPage: false, isFirstPage: true, …}
    //     fetchResponseFn?.(res);
    //   })
    //   .catch(err => {
    //     // showFailToast(err?.msg || "请求失败");
    //     // console.log("!!!2", err);
    //     fetchErrorFn?.(err);
    //   })
    //   .finally(() => {
    //     // console.log("!!3");
    //     loading.value = false;
    //     fetchFinallyFn?.(null);
    //   });
  };

  return {
    loading,
    data,
    error,
    response,
    execute,
    onFetchResponse,
    onFetchError,
    onFetchFinally
  };
}
1