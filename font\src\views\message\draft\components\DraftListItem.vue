<template>
  <div class="p-4">
    <div class="flex justify-between">
      <p class="title">{{ row.postTitle }}</p>
      <van-checkbox
        v-show="showDelete"
        :ref="el => (checkboxRefs[row.discussionId] = el)"
        :name="row.discussionId"
        @click.stop
      >
        <template #icon>
          <van-icon name="check" />
        </template>
      </van-checkbox>
    </div>
    <p class="mt-2 content">
      {{ row.postContent }}
    </p>
    <p class="time mt-3 text-right">
      {{ formatDate(row.updateTime) }}
    </p>
  </div>
</template>
<script setup lang="ts">
import { Discussion } from "@/views/message/message";
import { formatDate } from "@/utils/DateUtils";
const props = defineProps<{
  row: Discussion;
  checkboxRefs: any;
  showDelete: boolean;
}>();
</script>

<style scoped>
.title {
  font-family: Roboto;
  font-size: 16px;
  font-weight: 500;
  line-height: 24px;
  letter-spacing: 0px;

  font-feature-settings: "kern" on;
  color: #1f2937;
}

.content {
  min-height: 42px;
  font-family: Roboto;
  font-size: 14px;
  font-weight: normal;
  line-height: 21px;
  letter-spacing: 0px;
  font-feature-settings: "kern" on;
  color: #4b5563;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.time {
  font-family: Roboto;
  font-size: 12px;
  font-weight: normal;
  line-height: 18px;
  letter-spacing: 0px;

  font-feature-settings: "kern" on;
  color: #9ca3af;
}

:deep(.van-checkbox__icon--checked .van-icon) {
  background-color: #7b7bf7;
  border-color: #7b7bf7;
}
</style>
