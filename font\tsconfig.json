{"compilerOptions": {"baseUrl": ".", "types": ["node", "vite/client"], "paths": {"@/*": ["src/*"]}, "resolveJsonModule": true, "esModuleInterop": true}, "files": [], "references": [{"path": "./tsconfig.app.json"}, {"path": "./tsconfig.node.json"}], "include": ["mock/*.ts", "src/**/*.ts", "src/**/*.tsx", "src/**/*.vue", "typings/*.d.ts", "vite.config.ts"], "exclude": ["dist", "**/*.js", "node_modules"]}