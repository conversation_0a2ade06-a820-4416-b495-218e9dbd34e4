import { defineConfig } from "vite";
import vue from "@vitejs/plugin-vue";
import tailwindcss from "@tailwindcss/vite";
import { fileURLToPath, URL } from "node:url";
import { VantResolver } from "unplugin-vue-components/resolvers";
import Components from "unplugin-vue-components/vite";
import path from "path";
import { createSvgIconsPlugin } from "vite-plugin-svg-icons";
import AutoImport from "unplugin-auto-import/vite";

// 当前工作目录路径
const root: string = process.cwd();

export default defineConfig({
  plugins: [
    vue(),
    tailwindcss(),
    // vant 组件自动按需引入
    Components({
      dts: "src/typings/components.d.ts",
      resolvers: [VantResolver()]
    }),
    AutoImport({
      imports: ["vue", "vue-router", "pinia", "vue-i18n"],
      dirs: ["src/stores", "src/components", "src/hooks"],
      dts: "src/typings/auto-import.d.ts"
    }),
    // svg icon
    createSvgIconsPlugin({
      // 指定图标文件夹
      iconDirs: [path.resolve(root, "src/icons/svg")],
      // 指定 symbolId 格式
      symbolId: "icon-[dir]-[name]"
    })
  ],
  resolve: {
    alias: {
      "@": fileURLToPath(new URL("./src", import.meta.url))
    }
  }
});
