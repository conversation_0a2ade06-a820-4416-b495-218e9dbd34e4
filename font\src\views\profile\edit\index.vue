<template>
  <div>
    <Top :type="type" />
    <ProfileForm v-model="profileForm" />
    <button class="save-btn" @click="submitProfile">保存</button>

    <div class="consent-group">
      <input
        id="consent"
        :checked="profileForm.isDefault"
        type="radio"
        name="consent"
        @click="profileForm.isDefault = !profileForm.isDefault"
      />
      <label for="consent">默认使用该信息</label>
    </div>
  </div>
</template>
<script setup lang="ts">
import ProfileForm from "@/views/profile/edit/components/ProfileForm.vue";
import Top from "@/views/profile/edit/top.vue";
import type { Profile } from "@/views/profile/profile";
import api from "@/api/index";
import { useData } from "@/hooks/useData";

const route = useRoute();

// type: create | edit
const type = computed(() => route.path.split("/").pop());

const profileForm = ref<Profile>({
  profileId: "",
  profileName: "",
  gender: 1, // 1:男 2:女
  profileDateType: 1, // 1:公历 2:农历 3:四柱
  solarBirth: "",
  lunarBirth: "",
  fourPillars: "",
  realSolarTime: "",
  latitude: "35.0", // 纬度
  longitude: "120.0", // 经度
  isDefault: true
});

// create profile
const {
  execute: executeCreateProfile,
  onFetchResponse: onFetchResponseCreateProfile
} = useData(api.profile.createProfile);
onFetchResponseCreateProfile(() => {});

// edit profile
const {
  execute: executeUpdateProfileById,
  onFetchResponse: onFetchResponseUpdateProfileById
} = useData(api.profile.updateProfileById);
onFetchResponseUpdateProfileById(() => {});

const submitProfile = async () => {
  if (type.value === "create") {
    await executeCreateProfile(profileForm.value);
  } else {
    await executeUpdateProfileById(profileForm.value);
  }
};

// 加载profile
const {
  execute: executeGetProfileById,
  onFetchResponse: onFetchResponseGetProfileById
} = useData(api.profile.getProfileById);
onFetchResponseGetProfileById(data => {
  profileForm.value = {
    ...data
    // format solarBirth
    // latitude: data.latitude.toFixed(2),
    // longitude: data.longitude.toFixed(2)
  };
});

onMounted(() => {
  if (type.value === "edit") {
    executeGetProfileById({
      id: route.query.id as string
    });
  }
});
</script>

<style scoped>
.save-btn {
  background-color: #8a82f1;
  color: white;
  padding: 12px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  width: 358px;
  font-size: 16px;
  transition: background-color 0.3s;

  display: block;
  margin: 20px auto 0;
}

.save-btn:hover {
  background-color: #7065d1;
}

.consent-group {
  margin-top: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: #555;
}

.consent-group input[type="radio"] {
  margin-right: 5px;
  accent-color: #8a82f1; /* 改变单选按钮选中时的颜色 */
}
</style>
