<template>
  <div class="h-full flex flex-col">
    <div class="p-6 flex items-center relative">
      <svg-icon name="go-back" class="absolute left-6" @click="goBack" />
      <p class="title mx-auto">{{ $t("user.password.reset") }}</p>
    </div>
    <div class="flex flex-col gap-4 px-4">
      <email ref="emailVerificationRef" />
      <van-form ref="formRef">
        <van-field
          v-model="formData.oldPassword"
          name="oldPassword"
          :label="$t('user.password.origin')"
          type="password"
          :placeholder="$t('user.password.originPlaceholder')"
          clearable
          label-align="top"
          :rules="[
            { required: true, message: $t('user.password.originPlaceholder') }
          ]"
        />
        <van-field
          v-model="formData.newPassword"
          name="newPassword"
          :label="$t('user.password.new')"
          :placeholder="$t('user.password.newPlaceholder')"
          clearable
          type="password"
          label-align="top"
          :rules="[
            { required: true, message: $t('user.password.newPlaceholder') }
          ]"
        />
        <van-field
          v-model="formData.confirmPassword"
          name="confirmPassword"
          :label="$t('user.password.confirm')"
          :placeholder="$t('user.password.confirmPlaceholder')"
          clearable
          type="password"
          label-align="top"
          :rules="[
            {
              validator: confirmPasswordValidator
            }
          ]"
        />
      </van-form>

      <button class="submit-btn mt-4" @click="submit">
        {{ $t("user.password.submit") }}
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useData } from "@/hooks/useData";
import api from "@/api/index";
import { useUserStore } from "@/store/modules/user";
import { showToast } from "vant";
import user from "@/api/module/user";

const { t } = useI18n();
const userStore = useUserStore();

const { userInfo } = storeToRefs(userStore);
const formRef = ref(null);
const formData = reactive({
  oldPassword: "",
  newPassword: "",
  confirmPassword: ""
});

const confirmPasswordValidator = val => {
  if (val.length === 0) {
    return t("user.password.confirmPlaceholder");
  }
  if (val === formData.newPassword) {
    return true;
  }
  return t("user.password.notSame");
};

const submit = async () => {
  // 表单校验结果
  let formValid = false;
  try {
    await formRef.value.validate();
    formValid = true;
  } catch (errors) {
    console.error("表单校验失败：", errors);
  }
  if (!formValid) {
    return;
  }

  await execute(formData);
};

const { error, data, loading, execute, onFetchResponse } = useData(
  api.login.resetPassword
);

onFetchResponse(() => {
  showToast("请重新登录");
  userStore.clearUserInfo();
  router.push("/login");
});

const router = useRouter();
const goBack = () => {
  router.back();
};
</script>

<style scoped>
.code-btn {
  margin-left: 10px;
  min-width: 110px;
  height: 48px;
  background-color: #7b68ee;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 16px;
  cursor: pointer;
}

.code-btn:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

.description {
  font-size: 14px;
  font-weight: normal;
  line-height: 20px;
  letter-spacing: 0px;
  font-feature-settings: "kern" on;
  color: #6b7280;
}

.submit-btn {
  width: 100%;
  height: 48px;
  background-color: #7b68ee;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 16px;
  cursor: pointer;
}

:deep(.van-cell) {
  padding: 10px 0;
}

:deep(.van-field__control) {
  width: 100%;
  height: 48px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 0 16px;
  font-size: 16px;
  outline: none;
}
</style>
