<template>
  <div class="nav-top">
    <van-icon name="arrow-left" class="color-[#4B5563]" @click="toUser" />
    <p class="pl-4 nav-title">{{ $t("profile.list.top") }}</p>
    <svg-icon
      name="add-profile"
      class-name="add-profile"
      @click="toCreateProfile"
    />
  </div>
</template>

<script setup lang="ts">
const router = useRouter();
const toUser = () => {
  router.push("/home");
};
const toCreateProfile = () => {
  router.push("/profile/create");
};
</script>
<style scoped>
.nav-top {
  width: 100%;
  height: 56px;

  display: flex;
  align-items: center;
  padding: 0 16px;
  flex-wrap: wrap;
  align-content: center;

  background: white;

  box-sizing: border-box;
  border-width: 0 0 1px 0;
  border-style: solid;
  border-color: #f3f4f6;
}

.nav-title {
  font-family: Roboto;
  font-size: 18px;
  font-weight: 500;
  line-height: 28px;
  letter-spacing: 0px;

  font-feature-settings: "kern" on;
  color: black;
}

.add-profile {
  margin-inline: auto 0;
  width: 32px;
  height: 32px;
}
</style>
