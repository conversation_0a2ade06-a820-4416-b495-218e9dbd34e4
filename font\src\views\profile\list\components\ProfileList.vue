<template>
  <div>
    <van-search
      v-model="profileListQueryDto.profileName"
      shape="round"
      placeholder="搜索档案"
      @update:model-value="inputChange"
    />
    <van-list
      v-model:loading="loading"
      v-model:error="error"
      :finished="finished"
      error-text="请求失败，点击重新加载"
    >
      <div v-for="item in profileList" :key="item.profileId">
        <profile-list-item :row="item" @deleteProfile="deleteProfile" />
      </div>
    </van-list>
  </div>
</template>
<script setup lang="ts">
import { useData } from "@/hooks/useData";
import api from "@/api/index";
import { Profile } from "@/views/profile/profile";
import { debounce } from "lodash";
import ProfileListItem from "@/views/profile/list/components/ProfileListItem.vue";
import { showToast } from "vant";

const inputChange = debounce(() => {
  execute(profileListQueryDto.value);
}, 600);

const profileListQueryDto = ref({
  profileName: ""
});

// 删除
const { execute: executeDeleteProfile } = useData(
  api.profile.deleteProfileById
);

const deleteProfile = async (profileId: string) => {
  await executeDeleteProfile({
    profileId: profileId
  });
  showToast("删除成功");
  execute(profileListQueryDto.value);
};

const finished = ref(true);
const { error, loading, execute, onFetchResponse } = useData(
  api.profile.getProfileList
);

const profileList = ref<Profile[]>([]);
onFetchResponse(data => {
  profileList.value = data;
  console.log(data);
});

onMounted(() => {
  execute(profileListQueryDto.value);
});
</script>

<style scoped></style>
