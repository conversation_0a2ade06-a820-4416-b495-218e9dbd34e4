<template>
  <div class="h-full flex flex-col">
    <message-top />
    <message-menu class="mt-3" />
    <message-tab class="mt-3" @updateActive="handleActive" />
    <discussion-list class="mt-[1px]" :active="active" />
    <button class="floating-btn" @click="handleClick">+</button>
  </div>
</template>

<script setup lang="ts">
import messageTop from "./top.vue";
import messageMenu from "./components/Menu.vue";
import messageTab from "./components/Tab.vue";
import discussionList from "./components/DiscussionList.vue";

// 当前消息类型 2: 已发布 3: 已回复
const active = ref(2);
const handleActive = (value: number) => {
  active.value = value;
};

// to publish
const router = useRouter();
const handleClick = () => {
  router.push("/message/publish");
};
</script>

<style scoped>
.floating-btn {
  width: 50px;
  height: 50px;

  position: fixed;
  bottom: 75px;
  left: 50%;
  transform: translateX(-50%);

  border-radius: calc(infinity * 1px);
  opacity: 100%;

  /* 自动布局 */
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 0;
  gap: 0;
  flex-wrap: wrap;
  align-content: center;
  border-radius: calc(infinity * 1px);

  background:
    linear-gradient(0deg, rgba(0, 0, 0, 0.001), rgba(0, 0, 0, 0.001)), #92abff;

  box-shadow:
    0px 4px 6px -4px rgba(0, 0, 0, 0.1),
    0px 10px 15px -3px rgba(0, 0, 0, 0.1);

  /* 放大 “+” 并设为白色 */
  font-size: 24px;
  color: #fff;
  font-weight: bold;
}
</style>
