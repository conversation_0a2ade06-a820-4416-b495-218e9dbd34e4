<template>
  <div class="menuBox">
    <div v-for="menuItem in menuButtons" :key="menuItem.text"
      class="w-1/3 flex flex-col justify-center items-center menu-item" @click="menuItem.func">
      <div class="icon-wrapper">
        <svg-icon :name="menuItem.icon" :class-name="menuItem.iconClass" />
      </div>
      <span class="menu-text">{{ menuItem.text }}</span>
    </div>
  </div>
</template>
<script setup lang="ts">
import { useData } from "@/hooks/useData";
import api from "@/api/index";
import type { Profile } from "@/views/profile/profile";

const router = useRouter();

// 获取默认档案
const {
  data: defaultProfileData,
  execute: executeGetDefaultProfile,
  onFetchResponse: onFetchDefaultProfile,
  onFetchError: onFetchDefaultProfileError
} = useData(api.profile.getDefaultProfile);

// 处理"我的八字"点击事件
const handleMyBaziClick = async () => {
  // 设置成功回调
  onFetchDefaultProfile((profile: Profile) => {
    if (profile && profile.profileId) {
      // 有默认档案，跳转到八字排盘结果页面
      router.push({
        name: "BaziResult",
        query: { profileId: profile.profileId }
      });
    } else {
      // 没有默认档案，跳转到创建档案页面
      router.push({ name: "createPaipan" });
    }
  });

  // 设置错误回调
  onFetchDefaultProfileError((error: any) => {
    // 如果获取默认档案失败，也跳转到创建档案页面
    console.error("获取默认档案失败:", error);
    router.push({ name: "createPaipan" });
  });

  // 执行请求
  await executeGetDefaultProfile({});
};

const menuButtons = [
  {
    text: "我的档案",
    icon: "my-profile",
    iconClass: "menu-icon blue-icon",
    func: () => {
      router.push({ name: "Profile" });
    }
  },
  {
    text: "我的运势",
    icon: "my-fortune",
    iconClass: "menu-icon purple-icon",
    func: () => { }
  },
  {
    text: "缘分合盘",
    icon: "love-match",
    iconClass: "menu-icon pink-icon",
    func: () => { }
  },
  {
    text: "流年财运",
    icon: "wealth-fortune",
    iconClass: "menu-icon yellow-icon",
    func: () => { }
  },
  {
    text: "爱情运势",
    icon: "love-fortune",
    iconClass: "menu-icon pink-icon",
    func: () => { }
  },
  {
    text: "我的八字",
    icon: "my-bazi",
    iconClass: "menu-icon red-icon",
    func: handleMyBaziClick
  }
];
</script>

<style scoped>
.menuBox {
  display: flex;
  flex-wrap: wrap;
  align-content: flex-start;
  align-self: stretch;
  padding: 20px 16px;
  border-radius: 12px;
  opacity: 1;
  background:
    linear-gradient(0deg, rgba(0, 0, 0, 0.001), rgba(0, 0, 0, 0.001)), #ffffff;
  box-sizing: border-box;
  border: 1px solid #e5e7eb;
  backdrop-filter: blur(6px);
  box-shadow: 0px 10px 15px -3px rgba(0, 0, 0, 0.1);
}

.menu-item {
  height: 80px;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.menu-item:hover {
  transform: scale(1.05);
}

.icon-wrapper {
  margin-bottom: 8px;
}

.menu-text {
  font-size: 12px;
  font-weight: 500;
  color: #374151;
  text-align: center;
}

/* 图标样式 */
:deep(.menu-icon) {
  width: 32px;
  height: 32px;
}

:deep(.blue-icon) {
  color: #60a5fa;
  /* 蓝色 - 我的档案 */
}

:deep(.purple-icon) {
  color: #a78bfa;
  /* 紫色 - 我的运势 */
}

:deep(.pink-icon) {
  color: #f472b6;
  /* 粉色 - 缘分合盘/爱情运势 */
}

:deep(.yellow-icon) {
  color: #fbbf24;
  /* 黄色 - 流年财运 */
}

:deep(.red-icon) {
  color: #ef4444;
  /* 红色 - 我的八字 */
}
</style>
