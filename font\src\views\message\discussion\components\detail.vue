<template>
  <div class="page-wrap">
    <swiper
      :loop="postImgUrls.length > 1"
      :modules="[Autoplay, Pagination, Navigation]"
      class="swiper-container"
    >
      <swiper-slide v-for="(img, index) in postImgUrls" :key="index">
        <img :src="img" class="swiper-image" />
      </swiper-slide>
    </swiper>
    <div class="p-4">
      <div class="flex">
        <img src="../../../../assets/avatar.png" width="40px" height="40px" />
        <div class="pl-4">
          <p class="username">{{ data?.username }}</p>
          <p class="publish-time">{{ formatDate(data?.publishTime) }}</p>
        </div>
      </div>
      <div class="pt-4">
        <p class="title">{{ data?.postTitle }}</p>
        <p class="pt-2 content">{{ data?.postContent }}</p>
      </div>
    </div>
    <button v-if="data?.status === 1 || data?.status === 2" class="wait-button">
      待回复
    </button>
    <button v-else class="main-button">查看回复</button>
  </div>
</template>

<script setup lang="ts">
import { useData } from "@/hooks/useData";
import api from "@/api/index";
import { Autoplay, Pagination, Navigation } from "swiper/modules";
import { Swiper, SwiperSlide } from "swiper/vue";
import "swiper/css";
import "swiper/css/pagination";
import "swiper/css/navigation";
import { formatDate } from "@/utils/DateUtils";

const postImgUrls = ref([]);

const { error, data, loading, execute, onFetchResponse } = useData(
  api.discussion.getDiscussionDetail
);
onFetchResponse(() => {
  postImgUrls.value = data.value.postImgUrls.split(",");
});

const route = useRoute();

onMounted(() => {
  execute({ discussionId: route.query.id as string });
});
</script>

<style lang="less" scoped>
.page-wrap {
  padding-top: 4px;
}

.swiper-container {
  width: 100%;
  height: 375px;

  .swiper-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    vertical-align: middle;
  }
}

.username {
  font-family: Roboto;
  font-size: 14px;
  font-weight: 500;
  line-height: 20px;
  letter-spacing: 0px;

  font-feature-settings: "kern" on;
  color: #000000;
}

.publish-time {
  font-family: Roboto;
  font-size: 12px;
  font-weight: normal;
  line-height: 16px;
  letter-spacing: 0px;

  font-feature-settings: "kern" on;
  color: #6b7280;
}

.title {
  font-family: Roboto;
  font-size: 18px;
  font-weight: bold;
  line-height: 28px;
  letter-spacing: 0px;

  font-feature-settings: "kern" on;
  color: #000000;
}

.content {
  font-family: Roboto;
  font-size: 14px;
  font-weight: normal;
  line-height: 24px;
  letter-spacing: 0px;

  font-feature-settings: "kern" on;
  color: #374151;
}

.wait-button {
  position: absolute;
  left: 101px;
  bottom: 42px;
  width: 173px;
  height: 48px;
  border-radius: 4px;
  opacity: 1;

  /* 自动布局 */
  display: flex;
  flex-direction: column;
  padding: 12px 0px;
  gap: 0px 10px;
  flex-wrap: wrap;
  align-content: center;
  font-family: Roboto;
  font-size: 16px;
  font-weight: 500;
  line-height: 24px;
  color: white;

  background: #9ca3af;
}

// ... existing code ...

.wait-button,
.main-button {
  position: absolute;
  left: 101px;
  bottom: 42px;
  width: 173px;
  height: 48px;
  border-radius: 4px;
  opacity: 1;
  display: flex;
  flex-direction: column;
  padding: 12px 0px;
  gap: 0px 10px;
  flex-wrap: wrap;
  align-content: center;
  font-family: Roboto;
  font-size: 16px;
  font-weight: 500;
  line-height: 24px;
  color: white;
}

.wait-button {
  background: #9ca3af;
}

.main-button {
  background: #7b7bf7;
}
</style>
